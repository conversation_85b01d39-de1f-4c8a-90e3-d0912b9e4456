package com.zte.uedm.digitalenergy.application.permission.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户组响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UserGroupResponse {
    
    /**
     * 用户组ID
     */
    private Long userGroupId;
    
    /**
     * 用户组名称
     */
    private String groupName;
    
    /**
     * 用户组描述
     */
    private String groupDescription;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 关联的角色列表
     */
    private List<RoleResponse> roles;
    
    /**
     * 用户组成员列表
     */
    private List<UserResponse> members;
    
    /**
     * 成员数量
     */
    private Integer memberCount;
    
    /**
     * 用户组权限列表（所有角色权限的并集）
     */
    private List<MenuPermissionResponse> menuPermissions;
}
