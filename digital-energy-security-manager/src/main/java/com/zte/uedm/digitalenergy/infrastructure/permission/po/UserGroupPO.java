package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户组持久化对象
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UserGroupPO {
    
    /**
     * 用户组ID
     */
    private Long userGroupId;
    
    /**
     * 用户组名称
     */
    private String groupName;
    
    /**
     * 用户组描述
     */
    private String groupDescription;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
}
