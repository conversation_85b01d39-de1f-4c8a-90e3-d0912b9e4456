package com.zte.uedm.digitalenergy.domain.permission.aggregate;

import com.zte.uedm.digitalenergy.domain.permission.valueobj.RoleType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色聚合根
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class Role {
    
    /**
     * 角色ID
     */
    private Long roleId;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 角色描述
     */
    private String roleDescription;
    
    /**
     * 角色类型
     */
    private RoleType roleType;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 关联的菜单ID列表
     */
    private List<String> menuIds;
    
    /**
     * 创建默认角色
     */
    public static Role createDefaultRole(String roleName, String roleDescription, String createBy) {
        Role role = new Role();
        role.setRoleName(roleName);
        role.setRoleDescription(roleDescription);
        role.setRoleType(RoleType.DEFAULT);
        role.setCreateBy(createBy);
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        return role;
    }
    
    /**
     * 创建自定义角色
     */
    public static Role createCustomRole(String roleName, String roleDescription, String createBy, List<String> menuIds) {
        Role role = new Role();
        role.setRoleName(roleName);
        role.setRoleDescription(roleDescription);
        role.setRoleType(RoleType.CUSTOM);
        role.setCreateBy(createBy);
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        role.setMenuIds(menuIds);
        return role;
    }
    
    /**
     * 更新角色信息
     */
    public void updateRole(String roleDescription, String updateBy, List<String> menuIds) {
        if (this.roleType.isDefault()) {
            throw new IllegalStateException("Default role cannot be modified");
        }
        this.roleDescription = roleDescription;
        this.updateBy = updateBy;
        this.updateTime = LocalDateTime.now();
        this.menuIds = menuIds;
    }
    
    /**
     * 判断是否可以删除
     */
    public boolean canDelete() {
        return this.roleType.isCustom();
    }
    
    /**
     * 判断是否为默认角色
     */
    public boolean isDefaultRole() {
        return this.roleType.isDefault();
    }
    
    /**
     * 判断是否为自定义角色
     */
    public boolean isCustomRole() {
        return this.roleType.isCustom();
    }
    
    /**
     * 验证角色名称
     */
    public static void validateRoleName(String roleName) {
        if (roleName == null || roleName.trim().isEmpty()) {
            throw new IllegalArgumentException("Role name cannot be empty");
        }
        if (roleName.length() > 20) {
            throw new IllegalArgumentException("Role name cannot exceed 20 characters");
        }
    }
    
    /**
     * 验证角色描述
     */
    public static void validateRoleDescription(String roleDescription) {
        if (roleDescription != null && roleDescription.length() > 200) {
            throw new IllegalArgumentException("Role description cannot exceed 200 characters");
        }
    }
}
