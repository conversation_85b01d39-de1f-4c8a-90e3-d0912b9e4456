package com.zte.uedm.digitalenergy.application.permission.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 菜单权限响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class MenuPermissionResponse {
    
    /**
     * 菜单ID
     */
    private String menuId;
    
    /**
     * 菜单名称
     */
    private String menuName;
    
    /**
     * 父菜单ID
     */
    private Long parentId;
    
    /**
     * 菜单路径
     */
    private String menuPath;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 菜单层级
     */
    private Integer menuLevel;
    
    /**
     * 是否有权限
     */
    private Boolean hasPermission;

    /**
     * 是否被选中（用于权限配置）
     */
    private Boolean selected;

    /**
     * 子菜单列表
     */
    private List<MenuPermissionResponse> children;
}
