package com.zte.uedm.digitalenergy.application.permission.dto.request;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 角色查询请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class RoleQueryRequest {
    
    /**
     * 页码
     */
    @Min(value = 1, message = "Page number must be greater than 0")
    private int pageNum = 1;
    
    /**
     * 页大小
     */
    @Min(value = 1, message = "Page size must be greater than 0")
    private int pageSize = 10;
    
    /**
     * 角色名称（模糊查询）
     */
    private String roleName;
    
    /**
     * 角色类型：DEFAULT-默认角色，CUSTOM-自定义角色
     */
    private String roleType;
}
