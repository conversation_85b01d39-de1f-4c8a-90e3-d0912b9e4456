package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户持久化对象
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UserPO {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户工号
     */
    private String userCode;
    
    /**
     * 组织机构
     */
    private String organization;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
}
