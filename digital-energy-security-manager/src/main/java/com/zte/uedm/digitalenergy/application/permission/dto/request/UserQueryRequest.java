package com.zte.uedm.digitalenergy.application.permission.dto.request;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 用户查询请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UserQueryRequest {
    
    /**
     * 页码
     */
    @Min(value = 1, message = "Page number must be greater than 0")
    private int pageNum = 1;
    
    /**
     * 页大小
     */
    @Min(value = 1, message = "Page size must be greater than 0")
    private int pageSize = 10;
    
    /**
     * 用户名（模糊查询）
     */
    private String username;
    
    /**
     * 用户工号（模糊查询）
     */
    private String userCode;
    
    /**
     * 组织机构（模糊查询）
     */
    private String organization;
}
