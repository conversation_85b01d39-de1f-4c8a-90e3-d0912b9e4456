package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户组成员关联持久化对象
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UserGroupMemberPO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户组ID
     */
    private Long userGroupId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
