package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.zte.uedm.digitalenergy.infrastructure.permission.po.MenuPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Mapper
public interface MenuMapper {
    
    /**
     * 插入菜单
     */
    int insert(MenuPO menuPO);
    
    /**
     * 更新菜单
     */
    int update(MenuPO menuPO);
    
    /**
     * 根据ID查询菜单
     */
    MenuPO selectById(@Param("menuId") String menuId);
    
    /**
     * 根据菜单名称查询菜单
     */
    MenuPO selectByMenuName(@Param("menuName") String menuName);
    
    /**
     * 查询所有菜单
     */
    List<MenuPO> selectAll();
    
    /**
     * 根据层级查询菜单
     */
    List<MenuPO> selectByMenuLevel(@Param("menuLevel") Integer menuLevel);
    
    /**
     * 根据父菜单ID查询子菜单
     */
    List<MenuPO> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 根据父菜单ID查询子菜单（按排序号排序）
     */
    List<MenuPO> selectByParentIdOrderBySortOrder(@Param("parentId") Long parentId);
    
    /**
     * 根据ID删除菜单
     */
    int deleteById(@Param("menuId") String menuId);
    
    /**
     * 批量删除菜单
     */
    int deleteByIds(@Param("menuIds") List<String> menuIds);
    
    /**
     * 检查菜单ID是否存在
     */
    int existsById(@Param("menuId") String menuId);
    
    /**
     * 检查菜单名称是否存在
     */
    int existsByMenuName(@Param("menuName") String menuName);
    
    /**
     * 根据菜单ID列表查询菜单
     */
    List<MenuPO> selectByIds(@Param("menuIds") List<String> menuIds);
    
    /**
     * 获取顶级菜单（第一层）
     */
    List<MenuPO> selectTopLevelMenus();
    
    /**
     * 根据路径查询菜单
     */
    MenuPO selectByMenuPath(@Param("menuPath") String menuPath);
    
    /**
     * 检查菜单是否有子菜单
     */
    int countChildrenByMenuId(@Param("menuId") String menuId);
}
