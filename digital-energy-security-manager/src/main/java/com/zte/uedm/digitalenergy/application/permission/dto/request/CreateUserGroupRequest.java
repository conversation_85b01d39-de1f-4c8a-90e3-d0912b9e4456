package com.zte.uedm.digitalenergy.application.permission.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建用户组请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class CreateUserGroupRequest {
    
    /**
     * 用户组名称
     */
    @NotBlank(message = "User group name cannot be empty")
    @Size(max = 50, message = "User group name cannot exceed 50 characters")
    private String groupName;
    
    /**
     * 用户组描述
     */
    @Size(max = 200, message = "User group description cannot exceed 200 characters")
    private String groupDescription;
    
    /**
     * 关联的角色ID列表（最多10个）
     */
    private List<Long> roleIds;
    
    /**
     * 用户组成员ID列表
     */
    private List<Long> memberIds;
}
