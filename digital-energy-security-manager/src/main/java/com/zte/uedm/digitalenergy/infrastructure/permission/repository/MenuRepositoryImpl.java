package com.zte.uedm.digitalenergy.infrastructure.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.aggregate.Menu;
import com.zte.uedm.digitalenergy.domain.permission.repository.MenuRepository;
import com.zte.uedm.digitalenergy.domain.permission.valueobj.MenuLevel;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.MenuMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.MenuPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 菜单仓储实现类
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@Repository
public class MenuRepositoryImpl implements MenuRepository {
    
    @Autowired
    private MenuMapper menuMapper;
    
    @Override
    @Transactional
    public Menu save(Menu menu) {
        MenuPO menuPO = convertToMenuPO(menu);
        if (menuMapper.selectById(menu.getMenuId()) == null) {
            // 新增菜单
            menuMapper.insert(menuPO);
            log.info("Created new menu: {}", menu.getMenuName());
        } else {
            // 更新菜单
            menuMapper.update(menuPO);
            log.info("Updated menu: {}", menu.getMenuName());
        }
        return menu;
    }
    
    @Override
    public Optional<Menu> findById(String menuId) {
        MenuPO menuPO = menuMapper.selectById(menuId);
        if (menuPO == null) {
            return Optional.empty();
        }
        return Optional.of(convertToMenu(menuPO));
    }
    
    @Override
    public Optional<Menu> findByMenuName(String menuName) {
        MenuPO menuPO = menuMapper.selectByMenuName(menuName);
        if (menuPO == null) {
            return Optional.empty();
        }
        return Optional.of(convertToMenu(menuPO));
    }
    
    @Override
    public List<Menu> findAll() {
        List<MenuPO> menuPOList = menuMapper.selectAll();
        return menuPOList.stream()
                .map(this::convertToMenu)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Menu> findByMenuLevel(MenuLevel menuLevel) {
        List<MenuPO> menuPOList = menuMapper.selectByMenuLevel(menuLevel.getLevel());
        return menuPOList.stream()
                .map(this::convertToMenu)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Menu> findByParentId(Long parentId) {
        List<MenuPO> menuPOList = menuMapper.selectByParentId(parentId);
        return menuPOList.stream()
                .map(this::convertToMenu)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Menu> findMenuTree() {
        List<MenuPO> menuPOList = menuMapper.selectMenuTree();
        return menuPOList.stream()
                .map(this::convertToMenu)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Menu> findSubMenuTree(String parentMenuId) {
        List<MenuPO> menuPOList = menuMapper.selectSubMenuTree(parentMenuId);
        return menuPOList.stream()
                .map(this::convertToMenu)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public void deleteById(String menuId) {
        menuMapper.deleteById(menuId);
        log.info("Deleted menu with ID: {}", menuId);
    }
    
    @Override
    @Transactional
    public void deleteByIds(List<String> menuIds) {
        menuMapper.deleteByIds(menuIds);
        log.info("Deleted {} menus", menuIds.size());
    }
    
    @Override
    public boolean existsById(String menuId) {
        return menuMapper.existsById(menuId);
    }
    
    @Override
    public boolean existsByMenuName(String menuName) {
        return menuMapper.existsByMenuName(menuName);
    }
    
    @Override
    public List<Menu> findByIds(List<String> menuIds) {
        List<MenuPO> menuPOList = menuMapper.selectByIds(menuIds);
        return menuPOList.stream()
                .map(this::convertToMenu)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Menu> findTopLevelMenus() {
        List<MenuPO> menuPOList = menuMapper.selectTopLevelMenus();
        return menuPOList.stream()
                .map(this::convertToMenu)
                .collect(Collectors.toList());
    }
    
    @Override
    public Optional<Menu> findByMenuPath(String menuPath) {
        MenuPO menuPO = menuMapper.selectByMenuPath(menuPath);
        if (menuPO == null) {
            return Optional.empty();
        }
        return Optional.of(convertToMenu(menuPO));
    }
    
    @Override
    public boolean hasChildren(String menuId) {
        return menuMapper.hasChildren(menuId);
    }
    
    @Override
    public List<String> findAllChildMenuIds(String menuId) {
        return menuMapper.selectAllChildMenuIds(menuId);
    }
    
    @Override
    public List<Menu> findByParentIdOrderBySortOrder(Long parentId) {
        List<MenuPO> menuPOList = menuMapper.selectByParentIdOrderBySortOrder(parentId);
        return menuPOList.stream()
                .map(this::convertToMenu)
                .collect(Collectors.toList());
    }
    
    /**
     * 转换为菜单领域对象
     */
    private Menu convertToMenu(MenuPO menuPO) {
        Menu menu = new Menu();
        menu.setMenuId(menuPO.getMenuId());
        menu.setMenuName(menuPO.getMenuName());
        menu.setParentId(menuPO.getParentId());
        menu.setMenuPath(menuPO.getMenuPath());
        menu.setSortOrder(menuPO.getSortOrder());
        menu.setMenuLevel(new MenuLevel(menuPO.getMenuLevel()));
        menu.setCreateTime(menuPO.getCreateTime());
        menu.setUpdateTime(menuPO.getUpdateTime());
        return menu;
    }
    
    /**
     * 转换为菜单PO
     */
    private MenuPO convertToMenuPO(Menu menu) {
        MenuPO menuPO = new MenuPO();
        menuPO.setMenuId(menu.getMenuId());
        menuPO.setMenuName(menu.getMenuName());
        menuPO.setParentId(menu.getParentId());
        menuPO.setMenuPath(menu.getMenuPath());
        menuPO.setSortOrder(menu.getSortOrder());
        menuPO.setMenuLevel(menu.getMenuLevel().getLevel());
        menuPO.setCreateTime(menu.getCreateTime() != null ? menu.getCreateTime() : LocalDateTime.now());
        menuPO.setUpdateTime(menu.getUpdateTime() != null ? menu.getUpdateTime() : LocalDateTime.now());
        return menuPO;
    }
}
