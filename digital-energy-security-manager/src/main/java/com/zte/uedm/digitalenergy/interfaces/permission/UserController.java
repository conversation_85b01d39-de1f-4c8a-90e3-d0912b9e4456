package com.zte.uedm.digitalenergy.interfaces.permission;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.digitalenergy.application.permission.dto.request.CreateUserRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UpdateUserRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UserQueryRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.response.MenuPermissionResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.UserResponse;
import com.zte.uedm.digitalenergy.application.permission.service.UserApplicationService;
import com.zte.uedm.digitalenergy.common.response.ResponseBean;
import com.zte.uedm.digitalenergy.common.response.ResponseBeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户管理控制器
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@RestController
@RequestMapping("/api/security-manager/v1/permission/users")
@Api(tags = "用户管理")
public class UserController {
    
    @Autowired
    private UserApplicationService userApplicationService;
    
    @ApiOperation("创建用户")
    @PostMapping
    public ResponseBean<UserResponse> createUser(
            @Valid @RequestBody CreateUserRequest request,
            @RequestHeader(value = "X-User-Code", defaultValue = "system") String currentUser) {
        try {
            log.info("Create user request: {} by user: {}", request.getUserCode(), currentUser);
            UserResponse response = userApplicationService.createUser(request, currentUser);
            return ResponseBeanUtils.success("User created successfully", response);
        } catch (IllegalArgumentException e) {
            log.warn("Create user failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("Create user failed: {}", e.getMessage());
            return ResponseBeanUtils.error(1003, e.getMessage());
        } catch (Exception e) {
            log.error("Create user error", e);
            return ResponseBeanUtils.systemError("Failed to create user");
        }
    }
    
    @ApiOperation("更新用户")
    @PutMapping("/{userId}")
    public ResponseBean<UserResponse> updateUser(
            @ApiParam("用户ID") @PathVariable @NotNull Long userId,
            @Valid @RequestBody UpdateUserRequest request,
            @RequestHeader(value = "X-User-Code", defaultValue = "system") String currentUser) {
        try {
            request.setUserId(userId);
            log.info("Update user request: {} by user: {}", userId, currentUser);
            UserResponse response = userApplicationService.updateUser(request, currentUser);
            return ResponseBeanUtils.success("User updated successfully", response);
        } catch (IllegalArgumentException e) {
            log.warn("Update user failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("Update user failed: {}", e.getMessage());
            return ResponseBeanUtils.error(1003, e.getMessage());
        } catch (Exception e) {
            log.error("Update user error", e);
            return ResponseBeanUtils.systemError("Failed to update user");
        }
    }
    
    @ApiOperation("删除用户")
    @DeleteMapping("/{userId}")
    public ResponseBean<Void> deleteUser(
            @ApiParam("用户ID") @PathVariable @NotNull Long userId) {
        try {
            log.info("Delete user request: {}", userId);
            userApplicationService.deleteUser(userId);
            return ResponseBeanUtils.success("User deleted successfully");
        } catch (IllegalArgumentException e) {
            log.warn("Delete user failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (Exception e) {
            log.error("Delete user error", e);
            return ResponseBeanUtils.systemError("Failed to delete user");
        }
    }
    
    @ApiOperation("批量删除用户")
    @DeleteMapping
    public ResponseBean<Void> deleteUsers(
            @ApiParam("用户ID列表") @RequestParam List<Long> userIds) {
        try {
            log.info("Batch delete users request: {}", userIds);
            userApplicationService.deleteUsers(userIds);
            return ResponseBeanUtils.success("Users deleted successfully");
        } catch (IllegalArgumentException e) {
            log.warn("Batch delete users failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (Exception e) {
            log.error("Batch delete users error", e);
            return ResponseBeanUtils.systemError("Failed to delete users");
        }
    }
    
    @ApiOperation("根据ID查询用户详情")
    @GetMapping("/{userId}")
    public ResponseBean<UserResponse> getUserById(
            @ApiParam("用户ID") @PathVariable @NotNull Long userId) {
        try {
            log.info("Get user by id request: {}", userId);
            UserResponse response = userApplicationService.getUserById(userId);
            return ResponseBeanUtils.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("Get user by id failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (Exception e) {
            log.error("Get user by id error", e);
            return ResponseBeanUtils.systemError("Failed to get user");
        }
    }
    
    @ApiOperation("分页查询用户列表")
    @GetMapping
    public ResponseBean<PageInfo<UserResponse>> getUsersByPage(@Valid UserQueryRequest request) {
        try {
            log.info("Get users by page request: {}", request);
            PageInfo<UserResponse> response = userApplicationService.getUsersByPage(request);
            return ResponseBeanUtils.success(response);
        } catch (Exception e) {
            log.error("Get users by page error", e);
            return ResponseBeanUtils.systemError("Failed to get users");
        }
    }

    @ApiOperation("获取用户的菜单权限树")
    @GetMapping("/{userId}/menu-permissions")
    public ResponseBean<List<MenuPermissionResponse>> getUserMenuPermissions(
            @ApiParam("用户ID") @PathVariable @NotNull Long userId) {
        try {
            log.info("Get user menu permissions request: {}", userId);
            List<MenuPermissionResponse> response = userApplicationService.getUserMenuPermissions(userId);
            return ResponseBeanUtils.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("Get user menu permissions failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (Exception e) {
            log.error("Get user menu permissions error", e);
            return ResponseBeanUtils.systemError("Failed to get user menu permissions");
        }
    }
}
