package com.zte.uedm.digitalenergy.application.permission.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.digitalenergy.application.permission.dto.request.CreateRoleRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.RoleQueryRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UpdateRoleRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.response.MenuPermissionResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.RoleResponse;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.Menu;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.Role;
import com.zte.uedm.digitalenergy.domain.permission.repository.MenuRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.RoleRepository;
import com.zte.uedm.digitalenergy.domain.permission.service.PermissionDomainService;
import com.zte.uedm.digitalenergy.domain.permission.valueobj.RoleType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色应用服务
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@Service
public class RoleApplicationService {
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Autowired
    private MenuRepository menuRepository;
    
    @Autowired
    private PermissionDomainService permissionDomainService;
    
    /**
     * 创建自定义角色
     */
    @Transactional
    public RoleResponse createRole(CreateRoleRequest request, String currentUser) {
        log.info("Creating role: {} by user: {}", request.getRoleName(), currentUser);
        
        // 验证业务规则
        permissionDomainService.validateCanCreateCustomRole();
        permissionDomainService.validateRoleNameUnique(request.getRoleName(), null);
        permissionDomainService.validateMenuIds(request.getMenuIds());
        
        // 验证输入参数
        Role.validateRoleName(request.getRoleName());
        Role.validateRoleDescription(request.getRoleDescription());
        
        // 创建角色
        Role role = Role.createCustomRole(
                request.getRoleName(),
                request.getRoleDescription(),
                currentUser,
                request.getMenuIds()
        );
        
        // 保存角色
        Role savedRole = roleRepository.save(role);
        
        log.info("Successfully created role: {} with ID: {}", savedRole.getRoleName(), savedRole.getRoleId());
        return convertToRoleResponse(savedRole);
    }
    
    /**
     * 更新角色
     */
    @Transactional
    public RoleResponse updateRole(UpdateRoleRequest request, String currentUser) {
        log.info("Updating role: {} by user: {}", request.getRoleId(), currentUser);
        
        // 查找角色
        Role role = roleRepository.findById(request.getRoleId())
                .orElseThrow(() -> new IllegalArgumentException("Role not found: " + request.getRoleId()));
        
        // 验证业务规则
        permissionDomainService.validateMenuIds(request.getMenuIds());
        
        // 验证输入参数
        Role.validateRoleDescription(request.getRoleDescription());
        
        // 更新角色
        role.updateRole(request.getRoleDescription(), currentUser, request.getMenuIds());
        
        // 保存角色
        Role savedRole = roleRepository.save(role);
        
        log.info("Successfully updated role: {}", savedRole.getRoleName());
        return convertToRoleResponse(savedRole);
    }
    
    /**
     * 删除角色
     */
    @Transactional
    public void deleteRole(Long roleId) {
        log.info("Deleting role: {}", roleId);
        
        // 验证角色是否可以删除
        permissionDomainService.validateRoleCanDelete(roleId);
        
        // 删除角色
        roleRepository.deleteById(roleId);
        
        log.info("Successfully deleted role: {}", roleId);
    }
    
    /**
     * 批量删除角色
     */
    @Transactional
    public void deleteRoles(List<Long> roleIds) {
        log.info("Batch deleting roles: {}", roleIds);
        
        // 验证每个角色是否可以删除
        for (Long roleId : roleIds) {
            permissionDomainService.validateRoleCanDelete(roleId);
        }
        
        // 批量删除角色
        roleRepository.deleteByIds(roleIds);
        
        log.info("Successfully deleted {} roles", roleIds.size());
    }
    
    /**
     * 根据ID查询角色详情
     */
    public RoleResponse getRoleById(Long roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("Role not found: " + roleId));
        
        return convertToRoleResponse(role);
    }
    
    /**
     * 分页查询角色列表
     */
    public PageInfo<RoleResponse> getRolesByPage(RoleQueryRequest request) {
        log.info("Querying roles by page: {}", request);
        
        RoleType roleType = null;
        if (request.getRoleType() != null && !request.getRoleType().isEmpty()) {
            roleType = RoleType.fromCode(request.getRoleType());
        }
        
        List<Role> roles = roleRepository.findByPage(
                request.getPageNum(),
                request.getPageSize(),
                request.getRoleName(),
                roleType
        );
        
        List<RoleResponse> roleResponses = roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());
        
        return new PageInfo<>(roleResponses);
    }
    
    /**
     * 获取所有角色列表
     */
    public List<RoleResponse> getAllRoles() {
        List<Role> roles = roleRepository.findAll();
        return roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取角色的菜单权限树
     */
    public List<MenuPermissionResponse> getRoleMenuPermissions(Long roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("Role not found: " + roleId));
        
        // 获取所有菜单
        List<Menu> allMenus = menuRepository.findMenuTree();
        
        // 构建菜单权限树
        return buildMenuPermissionTree(allMenus, role.getMenuIds());
    }
    
    /**
     * 获取所有菜单权限树（用于角色权限配置）
     */
    public List<MenuPermissionResponse> getAllMenuPermissions() {
        List<Menu> allMenus = menuRepository.findMenuTree();
        return buildMenuPermissionTree(allMenus, null);
    }
    
    /**
     * 转换为角色响应DTO
     */
    private RoleResponse convertToRoleResponse(Role role) {
        RoleResponse response = new RoleResponse();
        response.setRoleId(role.getRoleId());
        response.setRoleName(role.getRoleName());
        response.setRoleDescription(role.getRoleDescription());
        response.setRoleType(role.getRoleType().getCode());
        response.setRoleTypeDescription(role.getRoleType().getDescription());
        response.setCreateTime(role.getCreateTime());
        response.setCreateBy(role.getCreateBy());
        response.setUpdateTime(role.getUpdateTime());
        response.setUpdateBy(role.getUpdateBy());
        response.setCanDelete(role.canDelete());
        
        // 设置关联统计信息
        List<Long> userIds = roleRepository.findUserIdsByRoleId(role.getRoleId());
        List<Long> userGroupIds = roleRepository.findUserGroupIdsByRoleId(role.getRoleId());
        response.setUserCount(userIds.size());
        response.setUserGroupCount(userGroupIds.size());
        
        return response;
    }
    
    /**
     * 构建菜单权限树
     */
    private List<MenuPermissionResponse> buildMenuPermissionTree(List<Menu> allMenus, List<String> roleMenuIds) {
        List<MenuPermissionResponse> result = new ArrayList<>();

        // 获取顶级菜单（第一层）
        List<Menu> topLevelMenus = allMenus.stream()
                .filter(menu -> menu.getMenuLevel().isTopLevel())
                .collect(Collectors.toList());

        for (Menu menu : topLevelMenus) {
            MenuPermissionResponse menuResponse = convertToMenuPermissionResponse(menu, roleMenuIds);

            // 递归构建子菜单
            List<MenuPermissionResponse> children = buildChildMenuPermissions(menu.getMenuId(), allMenus, roleMenuIds);
            if (!children.isEmpty()) {
                menuResponse.setChildren(children);
            }

            result.add(menuResponse);
        }

        return result;
    }

    /**
     * 递归构建子菜单权限
     */
    private List<MenuPermissionResponse> buildChildMenuPermissions(String parentMenuId, List<Menu> allMenus, List<String> roleMenuIds) {
        List<MenuPermissionResponse> children = new ArrayList<>();

        // 查找子菜单（菜单ID以父菜单ID开头且长度比父菜单ID多2位）
        List<Menu> childMenus = allMenus.stream()
                .filter(menu -> menu.getMenuId().startsWith(parentMenuId)
                        && menu.getMenuId().length() == parentMenuId.length() + 2
                        && !menu.getMenuId().equals(parentMenuId))
                .collect(Collectors.toList());

        for (Menu childMenu : childMenus) {
            MenuPermissionResponse childResponse = convertToMenuPermissionResponse(childMenu, roleMenuIds);

            // 递归处理子菜单
            List<MenuPermissionResponse> grandChildren = buildChildMenuPermissions(childMenu.getMenuId(), allMenus, roleMenuIds);
            if (!grandChildren.isEmpty()) {
                childResponse.setChildren(grandChildren);
            }

            children.add(childResponse);
        }

        return children;
    }

    /**
     * 转换为菜单权限响应DTO
     */
    private MenuPermissionResponse convertToMenuPermissionResponse(Menu menu, List<String> roleMenuIds) {
        MenuPermissionResponse response = new MenuPermissionResponse();
        response.setMenuId(menu.getMenuId());
        response.setMenuName(menu.getMenuName());
        response.setMenuPath(menu.getMenuPath());
        response.setMenuLevel(menu.getMenuLevel().getLevel());
        response.setSortOrder(menu.getSortOrder());
        response.setHasPermission(roleMenuIds != null && roleMenuIds.contains(menu.getMenuId()));
        response.setSelected(roleMenuIds != null && roleMenuIds.contains(menu.getMenuId()));
        return response;
    }
}
