package com.zte.uedm.digitalenergy.common.response;

/**
 * 统一返回状态码枚举
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public enum ResultCode {
    
    // 成功状态码
    SUCCESS(0, "Success"),
    
    // 通用错误状态码 (1000-1999)
    SYSTEM_ERROR(1000, "System error"),
    PARAM_ERROR(1001, "Parameter error"),
    DATA_NOT_FOUND(1002, "Data not found"),
    OPERATION_FAILED(1003, "Operation failed"),
    UNAUTHORIZED(1004, "Unauthorized access"),
    FORBIDDEN(1005, "Access forbidden"),
    
    // 权限管理相关错误状态码 (2000-2999)
    ROLE_NOT_FOUND(2001, "Role not found"),
    ROLE_NAME_EXISTS(2002, "Role name already exists"),
    ROLE_CANNOT_DELETE(2003, "Role cannot be deleted, it is associated with users or groups"),
    ROLE_LIMIT_EXCEEDED(2004, "Role limit exceeded, maximum 1000 roles allowed"),
    DEFAULT_ROLE_CANNOT_MODIFY(2005, "Default role cannot be modified or deleted"),
    
    USER_NOT_FOUND(2011, "User not found"),
    USER_CODE_EXISTS(2012, "User code already exists"),
    USER_ROLE_LIMIT_EXCEEDED(2013, "User role limit exceeded, maximum 10 roles allowed"),
    USER_NOT_VALID(2014, "User is not a valid ZTE employee"),
    
    USER_GROUP_NOT_FOUND(2021, "User group not found"),
    USER_GROUP_NAME_EXISTS(2022, "User group name already exists"),
    USER_GROUP_LIMIT_EXCEEDED(2023, "User group limit exceeded, maximum 1000 groups allowed"),
    USER_GROUP_ROLE_LIMIT_EXCEEDED(2024, "User group role limit exceeded, maximum 10 roles allowed"),
    
    MENU_NOT_FOUND(2031, "Menu not found"),
    MENU_PERMISSION_DENIED(2032, "Menu permission denied");
    
    private final int code;
    private final String message;
    
    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
}
