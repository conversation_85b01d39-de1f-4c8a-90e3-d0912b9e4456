package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.zte.uedm.digitalenergy.infrastructure.permission.po.RoleMenuPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单关联Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Mapper
public interface RoleMenuMapper {
    
    /**
     * 批量插入角色菜单关联
     */
    int batchInsert(@Param("roleMenuList") List<RoleMenuPO> roleMenuList);
    
    /**
     * 根据角色ID删除关联
     */
    int deleteByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据菜单ID删除关联
     */
    int deleteByMenuId(@Param("menuId") String menuId);
    
    /**
     * 根据角色ID查询菜单ID列表
     */
    List<String> selectMenuIdsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据菜单ID查询角色ID列表
     */
    List<Long> selectRoleIdsByMenuId(@Param("menuId") String menuId);
    
    /**
     * 检查角色菜单关联是否存在
     */
    int existsByRoleIdAndMenuId(@Param("roleId") Long roleId, @Param("menuId") String menuId);
}
