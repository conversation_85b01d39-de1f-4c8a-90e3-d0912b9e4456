package com.zte.uedm.digitalenergy.application.permission.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.digitalenergy.application.permission.dto.request.CreateUserGroupRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UpdateUserGroupRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UserGroupQueryRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.response.MenuPermissionResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.UserGroupResponse;

import java.util.List;

/**
 * 用户组应用服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public interface UserGroupService {
    
    /**
     * 创建用户组
     */
    UserGroupResponse createUserGroup(CreateUserGroupRequest request, String currentUser);
    
    /**
     * 更新用户组
     */
    UserGroupResponse updateUserGroup(UpdateUserGroupRequest request, String currentUser);
    
    /**
     * 删除用户组
     */
    void deleteUserGroup(Long userGroupId);

    /**
     * 批量删除用户组
     */
    void deleteUserGroups(List<Long> userGroupIds);

    /**
     * 根据ID查询用户组详情
     */
    UserGroupResponse getUserGroupById(Long userGroupId);
    
    /**
     * 分页查询用户组列表
     */
    PageInfo<UserGroupResponse> getUserGroupsByPage(UserGroupQueryRequest request);
    
    /**
     * 获取所有用户组列表
     */
    List<UserGroupResponse> getAllUserGroups();
    
    /**
     * 获取用户组的菜单权限树
     */
    List<MenuPermissionResponse> getUserGroupMenuPermissions(Long userGroupId);
}
