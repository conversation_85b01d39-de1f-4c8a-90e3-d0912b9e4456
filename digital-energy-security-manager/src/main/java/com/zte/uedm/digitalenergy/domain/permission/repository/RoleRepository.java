package com.zte.uedm.digitalenergy.domain.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.aggregate.Role;
import com.zte.uedm.digitalenergy.domain.permission.valueobj.RoleType;

import java.util.List;
import java.util.Optional;

/**
 * 角色仓储接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public interface RoleRepository {
    
    /**
     * 保存角色
     */
    Role save(Role role);
    
    /**
     * 根据ID查找角色
     */
    Optional<Role> findById(Long roleId);
    
    /**
     * 根据角色名称查找角色
     */
    Optional<Role> findByRoleName(String roleName);
    
    /**
     * 根据角色类型查找角色列表
     */
    List<Role> findByRoleType(RoleType roleType);
    
    /**
     * 查找所有角色
     */
    List<Role> findAll();
    
    /**
     * 分页查询角色
     */
    List<Role> findByPage(int pageNum, int pageSize, String roleName, RoleType roleType);
    
    /**
     * 统计角色总数
     */
    long count();
    
    /**
     * 统计指定类型的角色数量
     */
    long countByRoleType(RoleType roleType);
    
    /**
     * 删除角色
     */
    void deleteById(Long roleId);
    
    /**
     * 批量删除角色
     */
    void deleteByIds(List<Long> roleIds);
    
    /**
     * 检查角色名称是否存在
     */
    boolean existsByRoleName(String roleName);
    
    /**
     * 检查角色是否被用户使用
     */
    boolean isRoleUsedByUsers(Long roleId);
    
    /**
     * 检查角色是否被用户组使用
     */
    boolean isRoleUsedByUserGroups(Long roleId);
    
    /**
     * 为角色分配菜单权限
     */
    void assignMenusToRole(Long roleId, List<String> menuIds);
    
    /**
     * 移除角色的菜单权限
     */
    void removeMenusFromRole(Long roleId);
    
    /**
     * 获取角色的菜单权限
     */
    List<String> findMenuIdsByRoleId(Long roleId);
    
    /**
     * 获取使用指定角色的用户列表
     */
    List<Long> findUserIdsByRoleId(Long roleId);
    
    /**
     * 获取使用指定角色的用户组列表
     */
    List<Long> findUserGroupIdsByRoleId(Long roleId);

    /**
     * 检查角色ID是否存在
     */
    boolean existsById(Long roleId);
}
