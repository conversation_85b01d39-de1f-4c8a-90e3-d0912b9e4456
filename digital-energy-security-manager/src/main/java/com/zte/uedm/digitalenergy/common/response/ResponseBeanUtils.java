package com.zte.uedm.digitalenergy.common.response;

/**
 * 统一返回响应工具类
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public class ResponseBeanUtils {
    
    /**
     * 成功返回（无数据）
     */
    public static <T> ResponseBean<T> success() {
        return new ResponseBean<>(ResultCode.SUCCESS);
    }
    
    /**
     * 成功返回（带数据）
     */
    public static <T> ResponseBean<T> success(T data) {
        return new ResponseBean<>(ResultCode.SUCCESS, data);
    }
    
    /**
     * 成功返回（自定义消息）
     */
    public static <T> ResponseBean<T> success(String message) {
        return new ResponseBean<>(ResultCode.SUCCESS.getCode(), message);
    }
    
    /**
     * 成功返回（自定义消息和数据）
     */
    public static <T> ResponseBean<T> success(String message, T data) {
        return new ResponseBean<>(ResultCode.SUCCESS.getCode(), message, data);
    }
    
    /**
     * 失败返回（使用错误码枚举）
     */
    public static <T> ResponseBean<T> error(ResultCode resultCode) {
        return new ResponseBean<>(resultCode);
    }
    
    /**
     * 失败返回（使用错误码枚举和数据）
     */
    public static <T> ResponseBean<T> error(ResultCode resultCode, T data) {
        return new ResponseBean<>(resultCode, data);
    }
    
    /**
     * 失败返回（自定义错误码和消息）
     */
    public static <T> ResponseBean<T> error(int code, String message) {
        return new ResponseBean<>(code, message);
    }
    
    /**
     * 失败返回（自定义错误码、消息和数据）
     */
    public static <T> ResponseBean<T> error(int code, String message, T data) {
        return new ResponseBean<>(code, message, data);
    }
    
    /**
     * 系统错误返回
     */
    public static <T> ResponseBean<T> systemError() {
        return new ResponseBean<>(ResultCode.SYSTEM_ERROR);
    }
    
    /**
     * 系统错误返回（自定义消息）
     */
    public static <T> ResponseBean<T> systemError(String message) {
        return new ResponseBean<>(ResultCode.SYSTEM_ERROR.getCode(), message);
    }
    
    /**
     * 参数错误返回
     */
    public static <T> ResponseBean<T> paramError() {
        return new ResponseBean<>(ResultCode.PARAM_ERROR);
    }
    
    /**
     * 参数错误返回（自定义消息）
     */
    public static <T> ResponseBean<T> paramError(String message) {
        return new ResponseBean<>(ResultCode.PARAM_ERROR.getCode(), message);
    }
    
    /**
     * 数据未找到返回
     */
    public static <T> ResponseBean<T> notFound() {
        return new ResponseBean<>(ResultCode.DATA_NOT_FOUND);
    }
    
    /**
     * 数据未找到返回（自定义消息）
     */
    public static <T> ResponseBean<T> notFound(String message) {
        return new ResponseBean<>(ResultCode.DATA_NOT_FOUND.getCode(), message);
    }
    
    /**
     * 未授权返回
     */
    public static <T> ResponseBean<T> unauthorized() {
        return new ResponseBean<>(ResultCode.UNAUTHORIZED);
    }
    
    /**
     * 禁止访问返回
     */
    public static <T> ResponseBean<T> forbidden() {
        return new ResponseBean<>(ResultCode.FORBIDDEN);
    }
}
