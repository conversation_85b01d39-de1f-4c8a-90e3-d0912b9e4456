package com.zte.uedm.digitalenergy.application.permission.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.digitalenergy.application.permission.dto.request.CreateRoleRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.RoleQueryRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UpdateRoleRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.response.MenuPermissionResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.RoleResponse;

import java.util.List;

/**
 * 角色应用服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public interface RoleService {
    
    /**
     * 创建角色
     */
    RoleResponse createRole(CreateRoleRequest request, String currentUser);
    
    /**
     * 更新角色
     */
    RoleResponse updateRole(UpdateRoleRequest request, String currentUser);
    
    /**
     * 删除角色
     */
    void deleteRole(Long roleId);

    /**
     * 批量删除角色
     */
    void deleteRoles(List<Long> roleIds);

    /**
     * 根据ID查询角色详情
     */
    RoleResponse getRoleById(Long roleId);
    
    /**
     * 分页查询角色列表
     */
    PageInfo<RoleResponse> getRolesByPage(RoleQueryRequest request);
    
    /**
     * 获取所有角色列表
     */
    List<RoleResponse> getAllRoles();
    
    /**
     * 获取角色的菜单权限树
     */
    List<MenuPermissionResponse> getRoleMenuPermissions(Long roleId);
    
    /**
     * 获取所有菜单权限树（用于角色权限配置）
     */
    List<MenuPermissionResponse> getAllMenuPermissions();
}
