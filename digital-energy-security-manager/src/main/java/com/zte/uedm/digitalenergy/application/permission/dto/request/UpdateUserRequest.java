package com.zte.uedm.digitalenergy.application.permission.dto.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 更新用户请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UpdateUserRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "User ID cannot be null")
    private Long userId;
    
    /**
     * 直接分配的角色ID列表（最多10个）
     */
    private List<Long> roleIds;
}
