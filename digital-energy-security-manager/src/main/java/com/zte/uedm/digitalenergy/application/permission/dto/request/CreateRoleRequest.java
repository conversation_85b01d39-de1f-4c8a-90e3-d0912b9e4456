package com.zte.uedm.digitalenergy.application.permission.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建角色请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class CreateRoleRequest {
    
    /**
     * 角色名称
     */
    @NotBlank(message = "Role name cannot be empty")
    @Size(max = 20, message = "Role name cannot exceed 20 characters")
    private String roleName;
    
    /**
     * 角色描述
     */
    @Size(max = 200, message = "Role description cannot exceed 200 characters")
    private String roleDescription;
    
    /**
     * 菜单权限ID列表
     */
    @NotNull(message = "Menu permissions cannot be null")
    private List<String> menuIds;
}
