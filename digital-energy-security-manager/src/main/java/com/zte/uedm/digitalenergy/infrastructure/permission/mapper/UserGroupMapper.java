package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserGroupPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户组Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Mapper
public interface UserGroupMapper {
    
    /**
     * 插入用户组
     */
    int insert(UserGroupPO userGroupPO);
    
    /**
     * 更新用户组
     */
    int update(UserGroupPO userGroupPO);
    
    /**
     * 根据ID查询用户组
     */
    UserGroupPO selectById(@Param("userGroupId") Long userGroupId);
    
    /**
     * 根据用户组名称查询用户组
     */
    UserGroupPO selectByGroupName(@Param("groupName") String groupName);
    
    /**
     * 查询所有用户组
     */
    List<UserGroupPO> selectAll();
    
    /**
     * 分页查询用户组
     */
    List<UserGroupPO> selectByPage(@Param("groupName") String groupName);
    
    /**
     * 统计用户组总数
     */
    long count();
    
    /**
     * 根据ID删除用户组
     */
    int deleteById(@Param("userGroupId") Long userGroupId);
    
    /**
     * 批量删除用户组
     */
    int deleteByIds(@Param("userGroupIds") List<Long> userGroupIds);
    
    /**
     * 检查用户组名称是否存在
     */
    int existsByGroupName(@Param("groupName") String groupName);
    
    /**
     * 根据角色ID查询用户组列表
     */
    List<UserGroupPO> selectByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据用户ID查询用户组列表
     */
    List<UserGroupPO> selectByUserId(@Param("userId") Long userId);
}
