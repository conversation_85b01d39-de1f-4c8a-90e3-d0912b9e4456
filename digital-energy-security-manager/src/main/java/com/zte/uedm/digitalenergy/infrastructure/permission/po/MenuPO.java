package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 菜单持久化对象
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class MenuPO {
    
    /**
     * 菜单ID（层级编码）
     */
    private String menuId;
    
    /**
     * 菜单名称
     */
    private String menuName;
    
    /**
     * 父菜单ID
     */
    private Long parentId;
    
    /**
     * 菜单路径
     */
    private String menuPath;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 菜单层级
     */
    private Integer menuLevel;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
