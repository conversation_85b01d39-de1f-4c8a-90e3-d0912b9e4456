package com.zte.uedm.digitalenergy.application.permission.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class RoleResponse {
    
    /**
     * 角色ID
     */
    private Long roleId;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 角色描述
     */
    private String roleDescription;
    
    /**
     * 角色类型：DEFAULT-默认角色，CUSTOM-自定义角色
     */
    private String roleType;
    
    /**
     * 角色类型描述
     */
    private String roleTypeDescription;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 关联的菜单权限列表
     */
    private List<MenuPermissionResponse> menuPermissions;
    
    /**
     * 关联的用户数量
     */
    private Integer userCount;
    
    /**
     * 关联的用户组数量
     */
    private Integer userGroupCount;
    
    /**
     * 是否可以删除
     */
    private Boolean canDelete;
}
