package com.zte.uedm.digitalenergy.application.permission.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建用户请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class CreateUserRequest {
    
    /**
     * 用户名
     */
    @NotBlank(message = "Username cannot be empty")
    @Size(max = 50, message = "Username cannot exceed 50 characters")
    private String username;
    
    /**
     * 用户工号
     */
    @NotBlank(message = "User code cannot be empty")
    @Size(max = 20, message = "User code cannot exceed 20 characters")
    private String userCode;
    
    /**
     * 组织机构
     */
    @Size(max = 200, message = "Organization cannot exceed 200 characters")
    private String organization;
    
    /**
     * 直接分配的角色ID列表（最多10个）
     */
    private List<Long> roleIds;
}
