package com.zte.uedm.digitalenergy.domain.permission.aggregate;

import com.zte.uedm.digitalenergy.domain.permission.valueobj.MenuLevel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜单聚合根
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class Menu {
    
    /**
     * 菜单ID（层级编码）
     */
    private String menuId;
    
    /**
     * 菜单名称
     */
    private String menuName;
    
    /**
     * 父菜单ID
     */
    private Long parentId;
    
    /**
     * 菜单路径
     */
    private String menuPath;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 菜单层级
     */
    private MenuLevel menuLevel;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 子菜单列表
     */
    private List<Menu> children;
    
    /**
     * 创建菜单
     */
    public static Menu createMenu(String menuId, String menuName, Long parentId, 
                                 String menuPath, Integer sortOrder, MenuLevel menuLevel) {
        validateMenuId(menuId);
        validateMenuName(menuName);
        
        Menu menu = new Menu();
        menu.setMenuId(menuId);
        menu.setMenuName(menuName);
        menu.setParentId(parentId);
        menu.setMenuPath(menuPath);
        menu.setSortOrder(sortOrder);
        menu.setMenuLevel(menuLevel);
        menu.setCreateTime(LocalDateTime.now());
        menu.setUpdateTime(LocalDateTime.now());
        return menu;
    }
    
    /**
     * 更新菜单信息
     */
    public void updateMenu(String menuName, String menuPath, Integer sortOrder) {
        validateMenuName(menuName);
        this.menuName = menuName;
        this.menuPath = menuPath;
        this.sortOrder = sortOrder;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 验证菜单ID格式
     */
    public static void validateMenuId(String menuId) {
        if (menuId == null || menuId.trim().isEmpty()) {
            throw new IllegalArgumentException("Menu ID cannot be empty");
        }
        
        // 验证层级编码格式：每层使用2位数字表示
        if (!menuId.matches("^\\d{2}(\\d{2})*$")) {
            throw new IllegalArgumentException("Menu ID must follow hierarchical format (e.g., 01, 0101, 010101)");
        }
        
        // 验证层级长度（最多4层，即8位数字）
        if (menuId.length() > 8) {
            throw new IllegalArgumentException("Menu ID cannot exceed 4 levels (8 digits)");
        }
    }
    
    /**
     * 验证菜单名称
     */
    public static void validateMenuName(String menuName) {
        if (menuName == null || menuName.trim().isEmpty()) {
            throw new IllegalArgumentException("Menu name cannot be empty");
        }
        if (menuName.length() > 100) {
            throw new IllegalArgumentException("Menu name cannot exceed 100 characters");
        }
    }
    
    /**
     * 根据菜单ID计算层级
     */
    public static MenuLevel calculateLevelFromId(String menuId) {
        validateMenuId(menuId);
        int level = menuId.length() / 2;
        return new MenuLevel(level);
    }
    
    /**
     * 判断是否为根菜单
     */
    public boolean isRootMenu() {
        return menuLevel.isRoot();
    }
    
    /**
     * 判断是否为顶级菜单
     */
    public boolean isTopLevelMenu() {
        return menuLevel.isTopLevel();
    }
    
    /**
     * 判断是否有子菜单
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }
    
    /**
     * 获取父菜单ID（从层级编码中提取）
     */
    public String getParentMenuId() {
        if (menuId.length() <= 2) {
            return null; // 顶级菜单没有父菜单
        }
        return menuId.substring(0, menuId.length() - 2);
    }
    
    /**
     * 生成子菜单ID
     */
    public String generateChildMenuId(int childIndex) {
        if (childIndex < 1 || childIndex > 99) {
            throw new IllegalArgumentException("Child index must be between 1 and 99");
        }
        return menuId + String.format("%02d", childIndex);
    }
}
