package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Mapper
public interface UserMapper {
    
    /**
     * 插入用户
     */
    int insert(UserPO userPO);
    
    /**
     * 更新用户
     */
    int update(UserPO userPO);
    
    /**
     * 根据ID查询用户
     */
    UserPO selectById(@Param("userId") Long userId);
    
    /**
     * 根据用户工号查询用户
     */
    UserPO selectByUserCode(@Param("userCode") String userCode);
    
    /**
     * 根据用户名查询用户
     */
    UserPO selectByUsername(@Param("username") String username);
    
    /**
     * 查询所有用户
     */
    List<UserPO> selectAll();
    
    /**
     * 分页查询用户
     */
    List<UserPO> selectByPage(@Param("username") String username, 
                             @Param("userCode") String userCode, 
                             @Param("organization") String organization);
    
    /**
     * 统计用户总数
     */
    long count();
    
    /**
     * 根据ID删除用户
     */
    int deleteById(@Param("userId") Long userId);
    
    /**
     * 批量删除用户
     */
    int deleteByIds(@Param("userIds") List<Long> userIds);
    
    /**
     * 检查用户工号是否存在
     */
    int existsByUserCode(@Param("userCode") String userCode);
    
    /**
     * 根据角色ID查询用户列表
     */
    List<UserPO> selectByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据用户组ID查询用户列表
     */
    List<UserPO> selectByUserGroupId(@Param("userGroupId") Long userGroupId);
}
