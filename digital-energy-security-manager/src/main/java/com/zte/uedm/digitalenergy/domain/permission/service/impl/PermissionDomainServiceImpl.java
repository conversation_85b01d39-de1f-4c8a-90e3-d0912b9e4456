package com.zte.uedm.digitalenergy.domain.permission.service.impl;

import com.zte.uedm.digitalenergy.domain.permission.aggregate.Role;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.User;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.UserGroup;
import com.zte.uedm.digitalenergy.domain.permission.repository.MenuRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.RoleRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserGroupRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserRepository;
import com.zte.uedm.digitalenergy.domain.permission.service.PermissionDomainServiceInterface;
import com.zte.uedm.digitalenergy.domain.permission.valueobj.RoleType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限领域服务实现
 *
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@Service
public class PermissionDomainServiceImpl implements PermissionDomainServiceInterface {
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private UserGroupRepository userGroupRepository;
    
    @Autowired
    private MenuRepository menuRepository;
    
    /**
     * 角色数量限制
     */
    public static final int MAX_CUSTOM_ROLES = 1000;
    
    /**
     * 用户组数量限制
     */
    public static final int MAX_USER_GROUPS = 1000;

    /**
     * 用户直接分配角色数量限制
     */
    public static final int MAX_USER_ROLES = 10;

    /**
     * 用户组角色分配数量限制
     */
    public static final int MAX_USER_GROUP_ROLES = 10;
    
    /**
     * 验证是否可以创建新的自定义角色
     */
    public void validateCanCreateCustomRole() {
        long customRoleCount = roleRepository.countByRoleType(RoleType.CUSTOM);
        if (customRoleCount >= MAX_CUSTOM_ROLES) {
            throw new IllegalStateException("Cannot create more custom roles. Maximum limit of " + MAX_CUSTOM_ROLES + " reached.");
        }
        log.info("Custom role count validation passed. Current count: {}", customRoleCount);
    }
    
    /**
     * 验证是否可以创建新的用户组
     */
    public void validateCanCreateUserGroup() {
        long userGroupCount = userGroupRepository.count();
        if (userGroupCount >= MAX_USER_GROUPS) {
            throw new IllegalStateException("Cannot create more user groups. Maximum limit of " + MAX_USER_GROUPS + " reached.");
        }
        log.info("User group count validation passed. Current count: {}", userGroupCount);
    }
    
    /**
     * 验证角色是否可以删除
     */
    public void validateRoleCanDelete(Long roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("Role not found: " + roleId));
        
        if (!role.canDelete()) {
            throw new IllegalStateException("Default role cannot be deleted: " + role.getRoleName());
        }
        
        if (roleRepository.isRoleUsedByUsers(roleId)) {
            throw new IllegalStateException("Role is being used by users and cannot be deleted: " + role.getRoleName());
        }
        
        if (roleRepository.isRoleUsedByUserGroups(roleId)) {
            throw new IllegalStateException("Role is being used by user groups and cannot be deleted: " + role.getRoleName());
        }
        
        log.info("Role deletion validation passed for role: {}", role.getRoleName());
    }
    
    /**
     * 计算用户的所有权限菜单
     */
    public Set<String> calculateUserPermissions(Long userId) {
        Set<String> allMenuIds = new HashSet<>();
        
        // 获取用户直接分配的角色权限
        List<Long> directRoleIds = userRepository.findDirectRoleIdsByUserId(userId);
        for (Long roleId : directRoleIds) {
            List<String> menuIds = roleRepository.findMenuIdsByRoleId(roleId);
            allMenuIds.addAll(menuIds);
        }
        
        // 获取用户通过用户组继承的角色权限
        List<Long> userGroupIds = userRepository.findUserGroupIdsByUserId(userId);
        for (Long userGroupId : userGroupIds) {
            List<Long> groupRoleIds = userGroupRepository.findRoleIdsByUserGroupId(userGroupId);
            for (Long roleId : groupRoleIds) {
                List<String> menuIds = roleRepository.findMenuIdsByRoleId(roleId);
                allMenuIds.addAll(menuIds);
            }
        }
        
        log.info("Calculated permissions for user {}: {} menu permissions", userId, allMenuIds.size());
        return allMenuIds;
    }
    
    /**
     * 计算用户组的所有权限菜单
     */
    public Set<String> calculateUserGroupPermissions(Long userGroupId) {
        Set<String> allMenuIds = new HashSet<>();
        
        List<Long> roleIds = userGroupRepository.findRoleIdsByUserGroupId(userGroupId);
        for (Long roleId : roleIds) {
            List<String> menuIds = roleRepository.findMenuIdsByRoleId(roleId);
            allMenuIds.addAll(menuIds);
        }
        
        log.info("Calculated permissions for user group {}: {} menu permissions", userGroupId, allMenuIds.size());
        return allMenuIds;
    }
    
    /**
     * 验证菜单ID是否有效
     */
    public void validateMenuIds(List<String> menuIds) {
        if (menuIds == null || menuIds.isEmpty()) {
            return;
        }
        
        List<String> invalidMenuIds = menuIds.stream()
                .filter(menuId -> !menuRepository.existsById(menuId))
                .collect(Collectors.toList());
        
        if (!invalidMenuIds.isEmpty()) {
            throw new IllegalArgumentException("Invalid menu IDs: " + invalidMenuIds);
        }
        
        log.info("Menu IDs validation passed for {} menus", menuIds.size());
    }
    
    /**
     * 验证用户是否为中兴员工（通过UAC接口）
     */
    public void validateZteEmployee(String userCode) {
        // TODO: 实现UAC接口调用验证用户合法性
        // 这里暂时使用简单的工号格式验证
        if (userCode == null || userCode.trim().isEmpty()) {
            throw new IllegalArgumentException("User code cannot be empty");
        }
        
        // 简单的工号格式验证（实际应该调用UAC接口）
        if (!userCode.matches("^\\d{8,12}$")) {
            throw new IllegalArgumentException("Invalid ZTE employee code format: " + userCode);
        }
        
        log.info("ZTE employee validation passed for user code: {}", userCode);
    }
    
    /**
     * 检查角色名称是否重复
     */
    public void validateRoleNameUnique(String roleName, Long excludeRoleId) {
        Role existingRole = roleRepository.findByRoleName(roleName).orElse(null);
        if (existingRole != null && !existingRole.getRoleId().equals(excludeRoleId)) {
            throw new IllegalArgumentException("Role name already exists: " + roleName);
        }
        log.info("Role name uniqueness validation passed: {}", roleName);
    }
    
    /**
     * 检查用户工号是否重复
     */
    public void validateUserCodeUnique(String userCode, Long excludeUserId) {
        User existingUser = userRepository.findByUserCode(userCode).orElse(null);
        if (existingUser != null && !existingUser.getUserId().equals(excludeUserId)) {
            throw new IllegalArgumentException("User code already exists: " + userCode);
        }
        log.info("User code uniqueness validation passed: {}", userCode);
    }
    
    /**
     * 检查用户组名称是否重复
     */
    public void validateUserGroupNameUnique(String groupName, Long excludeUserGroupId) {
        UserGroup existingUserGroup = userGroupRepository.findByGroupName(groupName).orElse(null);
        if (existingUserGroup != null && !existingUserGroup.getUserGroupId().equals(excludeUserGroupId)) {
            throw new IllegalArgumentException("User group name already exists: " + groupName);
        }
        log.info("User group name uniqueness validation passed: {}", groupName);
    }

    /**
     * 验证用户角色分配限制
     */
    public void validateUserRoleLimit(List<Long> roleIds) {
        if (roleIds != null && roleIds.size() > MAX_USER_ROLES) {
            throw new IllegalArgumentException("User cannot have more than " + MAX_USER_ROLES + " roles");
        }
        log.info("User role limit validation passed for {} roles", roleIds != null ? roleIds.size() : 0);
    }

    /**
     * 验证用户组角色分配限制
     */
    public void validateUserGroupRoleLimit(List<Long> roleIds) {
        if (roleIds != null && roleIds.size() > MAX_USER_GROUP_ROLES) {
            throw new IllegalArgumentException("User group cannot have more than " + MAX_USER_GROUP_ROLES + " roles");
        }
        log.info("User group role limit validation passed for {} roles", roleIds != null ? roleIds.size() : 0);
    }

    /**
     * 验证角色ID列表是否有效
     */
    public void validateRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return;
        }

        List<Long> invalidRoleIds = new ArrayList<>();
        for (Long roleId : roleIds) {
            if (!roleRepository.existsById(roleId)) {
                invalidRoleIds.add(roleId);
            }
        }

        if (!invalidRoleIds.isEmpty()) {
            throw new IllegalArgumentException("Invalid role IDs: " + invalidRoleIds);
        }

        log.info("Role IDs validation passed for {} roles", roleIds.size());
    }

    /**
     * 验证用户ID列表是否有效
     */
    public void validateUserIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }

        List<Long> invalidUserIds = new ArrayList<>();
        for (Long userId : userIds) {
            if (!userRepository.existsById(userId)) {
                invalidUserIds.add(userId);
            }
        }

        if (!invalidUserIds.isEmpty()) {
            throw new IllegalArgumentException("Invalid user IDs: " + invalidUserIds);
        }

        log.info("User IDs validation passed for {} users", userIds.size());
    }
}
