package com.zte.uedm.digitalenergy.application.permission.dto.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 更新角色请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UpdateRoleRequest {
    
    /**
     * 角色ID
     */
    @NotNull(message = "Role ID cannot be null")
    private Long roleId;
    
    /**
     * 角色描述
     */
    @Size(max = 200, message = "Role description cannot exceed 200 characters")
    private String roleDescription;
    
    /**
     * 菜单权限ID列表
     */
    @NotNull(message = "Menu permissions cannot be null")
    private List<String> menuIds;
}
