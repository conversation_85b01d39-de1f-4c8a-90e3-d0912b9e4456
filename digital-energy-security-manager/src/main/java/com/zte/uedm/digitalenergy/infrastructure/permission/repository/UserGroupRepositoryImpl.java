package com.zte.uedm.digitalenergy.infrastructure.permission.repository;

import com.github.pagehelper.PageHelper;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.UserGroup;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserGroupRepository;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserGroupMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserGroupMemberMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserGroupRoleMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserGroupPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户组仓储实现类
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@Repository
public class UserGroupRepositoryImpl implements UserGroupRepository {
    
    @Autowired
    private UserGroupMapper userGroupMapper;
    
    @Autowired
    private UserGroupRoleMapper userGroupRoleMapper;
    
    @Autowired
    private UserGroupMemberMapper userGroupMemberMapper;
    
    @Override
    @Transactional
    public UserGroup save(UserGroup userGroup) {
        if (userGroup.getUserGroupId() == null) {
            // 新增用户组
            UserGroupPO userGroupPO = convertToUserGroupPO(userGroup);
            userGroupMapper.insert(userGroupPO);
            userGroup.setUserGroupId(userGroupPO.getUserGroupId());
            log.info("Created new user group: {}", userGroup.getGroupName());
            
            // 保存用户组角色关联
            if (userGroup.getRoleIds() != null && !userGroup.getRoleIds().isEmpty()) {
                assignRolesToUserGroup(userGroup.getUserGroupId(), userGroup.getRoleIds());
            }
            
            // 保存用户组成员关联
            if (userGroup.getMemberIds() != null && !userGroup.getMemberIds().isEmpty()) {
                addMembersToUserGroup(userGroup.getUserGroupId(), userGroup.getMemberIds());
            }
        } else {
            // 更新用户组
            UserGroupPO userGroupPO = convertToUserGroupPO(userGroup);
            userGroupMapper.update(userGroupPO);
            log.info("Updated user group: {}", userGroup.getGroupName());
            
            // 更新用户组角色关联
            userGroupRoleMapper.deleteByUserGroupId(userGroup.getUserGroupId());
            if (userGroup.getRoleIds() != null && !userGroup.getRoleIds().isEmpty()) {
                assignRolesToUserGroup(userGroup.getUserGroupId(), userGroup.getRoleIds());
            }
            
            // 更新用户组成员关联
            userGroupMemberMapper.deleteByUserGroupId(userGroup.getUserGroupId());
            if (userGroup.getMemberIds() != null && !userGroup.getMemberIds().isEmpty()) {
                addMembersToUserGroup(userGroup.getUserGroupId(), userGroup.getMemberIds());
            }
        }
        return userGroup;
    }
    
    @Override
    public Optional<UserGroup> findById(Long userGroupId) {
        UserGroupPO userGroupPO = userGroupMapper.selectById(userGroupId);
        if (userGroupPO == null) {
            return Optional.empty();
        }
        UserGroup userGroup = convertToUserGroup(userGroupPO);
        // 加载用户组角色
        List<Long> roleIds = userGroupRoleMapper.selectRoleIdsByUserGroupId(userGroupId);
        userGroup.setRoleIds(roleIds);
        // 加载用户组成员
        List<Long> memberIds = userGroupMemberMapper.selectMemberIdsByUserGroupId(userGroupId);
        userGroup.setMemberIds(memberIds);
        return Optional.of(userGroup);
    }
    
    @Override
    public Optional<UserGroup> findByGroupName(String groupName) {
        UserGroupPO userGroupPO = userGroupMapper.selectByGroupName(groupName);
        if (userGroupPO == null) {
            return Optional.empty();
        }
        UserGroup userGroup = convertToUserGroup(userGroupPO);
        // 加载用户组角色
        List<Long> roleIds = userGroupRoleMapper.selectRoleIdsByUserGroupId(userGroup.getUserGroupId());
        userGroup.setRoleIds(roleIds);
        // 加载用户组成员
        List<Long> memberIds = userGroupMemberMapper.selectMemberIdsByUserGroupId(userGroup.getUserGroupId());
        userGroup.setMemberIds(memberIds);
        return Optional.of(userGroup);
    }
    
    @Override
    public List<UserGroup> findAll() {
        List<UserGroupPO> userGroupPOList = userGroupMapper.selectAll();
        return userGroupPOList.stream()
                .map(this::convertToUserGroup)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<UserGroup> findByPage(int pageNum, int pageSize, String groupName) {
        PageHelper.startPage(pageNum, pageSize);
        List<UserGroupPO> userGroupPOList = userGroupMapper.selectByPage(groupName);
        return userGroupPOList.stream()
                .map(this::convertToUserGroup)
                .collect(Collectors.toList());
    }
    
    @Override
    public long count() {
        return userGroupMapper.count();
    }
    
    @Override
    @Transactional
    public void deleteById(Long userGroupId) {
        // 删除用户组角色关联
        userGroupRoleMapper.deleteByUserGroupId(userGroupId);
        // 删除用户组成员关联
        userGroupMemberMapper.deleteByUserGroupId(userGroupId);
        // 删除用户组
        userGroupMapper.deleteById(userGroupId);
        log.info("Deleted user group with ID: {}", userGroupId);
    }
    
    @Override
    @Transactional
    public void deleteByIds(List<Long> userGroupIds) {
        for (Long userGroupId : userGroupIds) {
            userGroupRoleMapper.deleteByUserGroupId(userGroupId);
            userGroupMemberMapper.deleteByUserGroupId(userGroupId);
        }
        userGroupMapper.deleteByIds(userGroupIds);
        log.info("Deleted {} user groups", userGroupIds.size());
    }
    
    @Override
    public boolean existsByGroupName(String groupName) {
        return userGroupMapper.existsByGroupName(groupName) > 0;
    }
    
    @Override
    @Transactional
    public void assignRolesToUserGroup(Long userGroupId, List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return;
        }
        userGroupMapper.assignRolesToUserGroup(userGroupId, roleIds);
        log.info("Assigned {} roles to user group {}", roleIds.size(), userGroupId);
    }
    
    @Override
    @Transactional
    public void removeRolesFromUserGroup(Long userGroupId) {
        userGroupMapper.removeRolesFromUserGroup(userGroupId);
        log.info("Removed all roles from user group {}", userGroupId);
    }
    
    @Override
    public List<Long> findRoleIdsByUserGroupId(Long userGroupId) {
        return userGroupMapper.findRoleIdsByUserGroupId(userGroupId);
    }
    
    @Override
    @Transactional
    public void addMembersToUserGroup(Long userGroupId, List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }
        userGroupMapper.addMembersToUserGroup(userGroupId, userIds);
        log.info("Added {} members to user group {}", userIds.size(), userGroupId);
    }
    
    @Override
    @Transactional
    public void removeMembersFromUserGroup(Long userGroupId, List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }
        userGroupMapper.removeMembersFromUserGroup(userGroupId, userIds);
        log.info("Removed {} members from user group {}", userIds.size(), userGroupId);
    }
    
    @Override
    @Transactional
    public void removeAllMembersFromUserGroup(Long userGroupId) {
        userGroupMapper.removeAllMembersFromUserGroup(userGroupId);
        log.info("Removed all members from user group {}", userGroupId);
    }
    
    @Override
    public List<Long> findMemberIdsByUserGroupId(Long userGroupId) {
        return userGroupMapper.findMemberIdsByUserGroupId(userGroupId);
    }
    
    @Override
    public List<String> findAllMenuIdsByUserGroupId(Long userGroupId) {
        return userGroupMapper.findAllMenuIdsByUserGroupId(userGroupId);
    }
    
    @Override
    public List<UserGroup> findByRoleId(Long roleId) {
        List<UserGroupPO> userGroupPOList = userGroupMapper.findByRoleId(roleId);
        return userGroupPOList.stream()
                .map(this::convertToUserGroup)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<UserGroup> findByUserId(Long userId) {
        List<UserGroupPO> userGroupPOList = userGroupMapper.findByUserId(userId);
        return userGroupPOList.stream()
                .map(this::convertToUserGroup)
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean isUserInGroup(Long userGroupId, Long userId) {
        return userGroupMapper.isUserInGroup(userGroupId, userId);
    }
    
    @Override
    public int countMembersByUserGroupId(Long userGroupId) {
        return userGroupMapper.countMembersByUserGroupId(userGroupId);
    }
    
    /**
     * 转换为用户组PO
     */
    private UserGroupPO convertToUserGroupPO(UserGroup userGroup) {
        UserGroupPO userGroupPO = new UserGroupPO();
        userGroupPO.setUserGroupId(userGroup.getUserGroupId());
        userGroupPO.setGroupName(userGroup.getGroupName());
        userGroupPO.setGroupDescription(userGroup.getGroupDescription());
        userGroupPO.setCreateTime(userGroup.getCreateTime());
        userGroupPO.setCreateBy(userGroup.getCreateBy());
        userGroupPO.setUpdateTime(userGroup.getUpdateTime());
        userGroupPO.setUpdateBy(userGroup.getUpdateBy());
        return userGroupPO;
    }
    
    /**
     * 转换为用户组领域对象
     */
    private UserGroup convertToUserGroup(UserGroupPO userGroupPO) {
        UserGroup userGroup = new UserGroup();
        userGroup.setUserGroupId(userGroupPO.getUserGroupId());
        userGroup.setGroupName(userGroupPO.getGroupName());
        userGroup.setGroupDescription(userGroupPO.getGroupDescription());
        userGroup.setCreateTime(userGroupPO.getCreateTime());
        userGroup.setCreateBy(userGroupPO.getCreateBy());
        userGroup.setUpdateTime(userGroupPO.getUpdateTime());
        userGroup.setUpdateBy(userGroupPO.getUpdateBy());
        return userGroup;
    }
}
