package com.zte.uedm.digitalenergy.domain.permission.service;

import java.util.List;
import java.util.Set;

/**
 * 权限领域服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public interface PermissionDomainServiceInterface {
    
    /**
     * 验证是否可以创建新的自定义角色
     */
    void validateCanCreateCustomRole();
    
    /**
     * 验证是否可以创建新的用户组
     */
    void validateCanCreateUserGroup();
    
    /**
     * 验证角色是否可以删除
     */
    void validateRoleCanDelete(Long roleId);
    
    /**
     * 计算用户的所有权限菜单
     */
    Set<String> calculateUserPermissions(Long userId);
    
    /**
     * 计算用户组的所有权限菜单
     */
    Set<String> calculateUserGroupPermissions(Long userGroupId);
    
    /**
     * 验证菜单ID是否有效
     */
    void validateMenuIds(List<String> menuIds);
    
    /**
     * 验证用户是否为中兴员工（通过UAC接口）
     */
    void validateZteEmployee(String userCode);
    
    /**
     * 验证角色名称唯一性
     */
    void validateRoleNameUnique(String roleName, Long excludeRoleId);
    
    /**
     * 验证用户工号唯一性
     */
    void validateUserCodeUnique(String userCode, Long excludeUserId);
    
    /**
     * 验证用户组名称唯一性
     */
    void validateUserGroupNameUnique(String groupName, Long excludeUserGroupId);
    
    /**
     * 验证用户角色分配限制
     */
    void validateUserRoleLimit(List<Long> roleIds);
    
    /**
     * 验证用户组角色分配限制
     */
    void validateUserGroupRoleLimit(List<Long> roleIds);
    
    /**
     * 验证角色ID列表是否有效
     */
    void validateRoleIds(List<Long> roleIds);
    
    /**
     * 验证用户ID列表是否有效
     */
    void validateUserIds(List<Long> userIds);
}
