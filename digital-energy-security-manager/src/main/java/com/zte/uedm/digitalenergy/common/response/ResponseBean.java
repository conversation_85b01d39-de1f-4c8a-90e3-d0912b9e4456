package com.zte.uedm.digitalenergy.common.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 统一返回响应对象
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseBean<T> {
    
    /**
     * 状态码
     */
    private int code;
    
    /**
     * 返回消息
     */
    private String message;
    
    /**
     * 返回数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private long timestamp;
    
    public ResponseBean() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public ResponseBean(int code, String message) {
        this();
        this.code = code;
        this.message = message;
    }
    
    public ResponseBean(int code, String message, T data) {
        this(code, message);
        this.data = data;
    }
    
    public ResponseBean(ResultCode resultCode) {
        this(resultCode.getCode(), resultCode.getMessage());
    }
    
    public ResponseBean(ResultCode resultCode, T data) {
        this(resultCode.getCode(), resultCode.getMessage(), data);
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return this.code == ResultCode.SUCCESS.getCode();
    }
}
