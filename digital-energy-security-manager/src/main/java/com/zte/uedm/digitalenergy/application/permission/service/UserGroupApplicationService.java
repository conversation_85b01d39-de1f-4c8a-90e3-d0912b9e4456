package com.zte.uedm.digitalenergy.application.permission.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.digitalenergy.application.permission.dto.request.CreateUserGroupRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UpdateUserGroupRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UserGroupQueryRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.response.MenuPermissionResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.RoleResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.UserGroupResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.UserResponse;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.Menu;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.Role;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.User;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.UserGroup;
import com.zte.uedm.digitalenergy.domain.permission.repository.MenuRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.RoleRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserGroupRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserRepository;
import com.zte.uedm.digitalenergy.domain.permission.service.PermissionDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户组应用服务
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@Service
public class UserGroupApplicationService {
    
    @Autowired
    private UserGroupRepository userGroupRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Autowired
    private MenuRepository menuRepository;
    
    @Autowired
    private PermissionDomainService permissionDomainService;
    
    /**
     * 创建用户组
     */
    @Transactional
    public UserGroupResponse createUserGroup(CreateUserGroupRequest request, String currentUser) {
        log.info("Creating user group: {} by user: {}", request.getGroupName(), currentUser);
        
        // 验证业务规则
        permissionDomainService.validateCanCreateUserGroup();
        permissionDomainService.validateUserGroupNameUnique(request.getGroupName(), null);
        
        // 验证角色分配限制
        if (request.getRoleIds() != null) {
            permissionDomainService.validateUserGroupRoleLimit(request.getRoleIds());
            permissionDomainService.validateRoleIds(request.getRoleIds());
        }
        
        // 验证成员是否存在
        if (request.getMemberIds() != null) {
            permissionDomainService.validateUserIds(request.getMemberIds());
        }
        
        // 创建用户组
        UserGroup userGroup = UserGroup.createUserGroup(
                request.getGroupName(),
                request.getGroupDescription(),
                currentUser,
                request.getRoleIds(),
                request.getMemberIds()
        );
        
        // 保存用户组
        UserGroup savedUserGroup = userGroupRepository.save(userGroup);
        
        log.info("Successfully created user group: {}", savedUserGroup.getGroupName());
        return convertToUserGroupResponse(savedUserGroup);
    }
    
    /**
     * 更新用户组
     */
    @Transactional
    public UserGroupResponse updateUserGroup(UpdateUserGroupRequest request, String currentUser) {
        log.info("Updating user group: {} by user: {}", request.getUserGroupId(), currentUser);
        
        // 查找用户组
        UserGroup userGroup = userGroupRepository.findById(request.getUserGroupId())
                .orElseThrow(() -> new IllegalArgumentException("User group not found: " + request.getUserGroupId()));
        
        // 验证角色分配限制
        if (request.getRoleIds() != null) {
            permissionDomainService.validateUserGroupRoleLimit(request.getRoleIds());
            permissionDomainService.validateRoleIds(request.getRoleIds());
        }
        
        // 验证成员是否存在
        if (request.getMemberIds() != null) {
            permissionDomainService.validateUserIds(request.getMemberIds());
        }
        
        // 更新用户组
        userGroup.updateUserGroup(
                request.getGroupDescription(),
                currentUser,
                request.getRoleIds(),
                request.getMemberIds()
        );
        
        // 保存用户组
        UserGroup savedUserGroup = userGroupRepository.save(userGroup);
        
        log.info("Successfully updated user group: {}", savedUserGroup.getGroupName());
        return convertToUserGroupResponse(savedUserGroup);
    }
    
    /**
     * 删除用户组
     */
    @Transactional
    public void deleteUserGroup(Long userGroupId) {
        log.info("Deleting user group: {}", userGroupId);
        
        // 验证用户组是否存在
        UserGroup userGroup = userGroupRepository.findById(userGroupId)
                .orElseThrow(() -> new IllegalArgumentException("User group not found: " + userGroupId));
        
        // 删除用户组（关联关系会自动删除）
        userGroupRepository.deleteById(userGroupId);
        
        log.info("Successfully deleted user group: {}", userGroup.getGroupName());
    }
    
    /**
     * 批量删除用户组
     */
    @Transactional
    public void deleteUserGroups(List<Long> userGroupIds) {
        log.info("Batch deleting user groups: {}", userGroupIds);
        
        for (Long userGroupId : userGroupIds) {
            deleteUserGroup(userGroupId);
        }
        
        log.info("Successfully deleted {} user groups", userGroupIds.size());
    }
    
    /**
     * 根据ID查询用户组详情
     */
    public UserGroupResponse getUserGroupById(Long userGroupId) {
        UserGroup userGroup = userGroupRepository.findById(userGroupId)
                .orElseThrow(() -> new IllegalArgumentException("User group not found: " + userGroupId));
        
        return convertToUserGroupResponse(userGroup);
    }
    
    /**
     * 分页查询用户组列表
     */
    public PageInfo<UserGroupResponse> getUserGroupsByPage(UserGroupQueryRequest request) {
        log.info("Querying user groups by page: {}", request);
        
        List<UserGroup> userGroups = userGroupRepository.findByPage(
                request.getPageNum(),
                request.getPageSize(),
                request.getGroupName()
        );
        
        List<UserGroupResponse> userGroupResponses = userGroups.stream()
                .map(this::convertToUserGroupResponse)
                .collect(Collectors.toList());
        
        return new PageInfo<>(userGroupResponses);
    }
    
    /**
     * 获取所有用户组列表
     */
    public List<UserGroupResponse> getAllUserGroups() {
        List<UserGroup> userGroups = userGroupRepository.findAll();
        return userGroups.stream()
                .map(this::convertToUserGroupResponse)
                .collect(Collectors.toList());
    }
    

    
    /**
     * 获取用户组的菜单权限树
     */
    public List<MenuPermissionResponse> getUserGroupMenuPermissions(Long userGroupId) {

        // 获取所有菜单
        List<Menu> allMenus = menuRepository.findMenuTree();

        // 获取用户组的权限菜单ID
        Set<String> userGroupMenuIds = permissionDomainService.calculateUserGroupPermissions(userGroupId);

        // 构建菜单权限树
        return buildMenuPermissionTree(allMenus, userGroupMenuIds);
    }
    
    /**
     * 转换为用户组响应DTO
     */
    private UserGroupResponse convertToUserGroupResponse(UserGroup userGroup) {
        UserGroupResponse response = new UserGroupResponse();
        response.setUserGroupId(userGroup.getUserGroupId());
        response.setGroupName(userGroup.getGroupName());
        response.setGroupDescription(userGroup.getGroupDescription());
        response.setCreateTime(userGroup.getCreateTime());
        response.setCreateBy(userGroup.getCreateBy());
        response.setUpdateTime(userGroup.getUpdateTime());
        response.setUpdateBy(userGroup.getUpdateBy());
        
        // 设置关联的角色
        if (userGroup.getRoleIds() != null) {
            List<RoleResponse> roles = new ArrayList<>();
            for (Long roleId : userGroup.getRoleIds()) {
                Role role = roleRepository.findById(roleId).orElse(null);
                if (role != null) {
                    roles.add(convertToRoleResponse(role));
                }
            }
            response.setRoles(roles);
        }
        
        // 设置用户组成员
        if (userGroup.getMemberIds() != null) {
            List<UserResponse> members = new ArrayList<>();
            for (Long memberId : userGroup.getMemberIds()) {
                User user = userRepository.findById(memberId).orElse(null);
                if (user != null) {
                    members.add(convertToUserResponse(user));
                }
            }
            response.setMembers(members);
            response.setMemberCount(members.size());
        } else {
            response.setMemberCount(0);
        }
        
        // 设置用户组权限
        Set<String> userGroupMenuIds = permissionDomainService.calculateUserGroupPermissions(userGroup.getUserGroupId());
        List<Menu> allMenus = menuRepository.findMenuTree();
        response.setMenuPermissions(buildMenuPermissionTree(allMenus, userGroupMenuIds));
        
        return response;
    }
    
    /**
     * 转换为角色响应DTO（简化版）
     */
    private RoleResponse convertToRoleResponse(Role role) {
        RoleResponse response = new RoleResponse();
        response.setRoleId(role.getRoleId());
        response.setRoleName(role.getRoleName());
        response.setRoleDescription(role.getRoleDescription());
        response.setRoleType(role.getRoleType().getCode());
        response.setRoleTypeDescription(role.getRoleType().getDescription());
        return response;
    }
    
    /**
     * 转换为用户响应DTO（简化版）
     */
    private UserResponse convertToUserResponse(User user) {
        UserResponse response = new UserResponse();
        response.setUserId(user.getUserId());
        response.setUsername(user.getUsername());
        response.setUserCode(user.getUserCode());
        response.setOrganization(user.getOrganization());
        return response;
    }
    
    /**
     * 构建菜单权限树
     */
    private List<MenuPermissionResponse> buildMenuPermissionTree(List<Menu> allMenus, Set<String> selectedMenuIds) {
        List<MenuPermissionResponse> result = new ArrayList<>();

        // 获取顶级菜单（第一层）
        List<Menu> topLevelMenus = allMenus.stream()
                .filter(menu -> menu.getMenuLevel().isTopLevel())
                .collect(Collectors.toList());

        for (Menu menu : topLevelMenus) {
            MenuPermissionResponse menuResponse = convertToMenuPermissionResponse(menu, selectedMenuIds);

            // 递归构建子菜单
            List<MenuPermissionResponse> children = buildChildMenuPermissions(menu.getMenuId(), allMenus, selectedMenuIds);
            if (!children.isEmpty()) {
                menuResponse.setChildren(children);
            }

            result.add(menuResponse);
        }

        return result;
    }

    /**
     * 递归构建子菜单权限
     */
    private List<MenuPermissionResponse> buildChildMenuPermissions(String parentMenuId, List<Menu> allMenus, Set<String> selectedMenuIds) {
        List<MenuPermissionResponse> children = new ArrayList<>();

        // 查找子菜单（菜单ID以父菜单ID开头且长度比父菜单ID多2位）
        List<Menu> childMenus = allMenus.stream()
                .filter(menu -> menu.getMenuId().startsWith(parentMenuId)
                        && menu.getMenuId().length() == parentMenuId.length() + 2
                        && !menu.getMenuId().equals(parentMenuId))
                .collect(Collectors.toList());

        for (Menu childMenu : childMenus) {
            MenuPermissionResponse childResponse = convertToMenuPermissionResponse(childMenu, selectedMenuIds);

            // 递归处理子菜单
            List<MenuPermissionResponse> grandChildren = buildChildMenuPermissions(childMenu.getMenuId(), allMenus, selectedMenuIds);
            if (!grandChildren.isEmpty()) {
                childResponse.setChildren(grandChildren);
            }

            children.add(childResponse);
        }

        return children;
    }

    /**
     * 转换为菜单权限响应DTO
     */
    private MenuPermissionResponse convertToMenuPermissionResponse(Menu menu, Set<String> selectedMenuIds) {
        MenuPermissionResponse response = new MenuPermissionResponse();
        response.setMenuId(menu.getMenuId());
        response.setMenuName(menu.getMenuName());
        response.setMenuPath(menu.getMenuPath());
        response.setMenuLevel(menu.getMenuLevel().getLevel());
        response.setSortOrder(menu.getSortOrder());
        response.setSelected(selectedMenuIds != null && selectedMenuIds.contains(menu.getMenuId()));
        response.setHasPermission(selectedMenuIds != null && selectedMenuIds.contains(menu.getMenuId()));
        return response;
    }
}
