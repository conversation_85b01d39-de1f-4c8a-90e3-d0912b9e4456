package com.zte.uedm.digitalenergy.application.permission.dto.response;

import lombok.Data;

/**
 * 权限来源响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class PermissionSourceResponse {
    
    /**
     * 菜单ID
     */
    private String menuId;
    
    /**
     * 菜单名称
     */
    private String menuName;
    
    /**
     * 权限来源类型：DIRECT-直接分配，INHERITED-用户组继承
     */
    private String sourceType;
    
    /**
     * 来源描述
     */
    private String sourceDescription;
    
    /**
     * 角色名称（如果是通过角色获得的权限）
     */
    private String roleName;
    
    /**
     * 用户组名称（如果是通过用户组继承的权限）
     */
    private String userGroupName;
}
