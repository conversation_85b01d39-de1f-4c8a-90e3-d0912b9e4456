package com.zte.uedm.digitalenergy.application.permission.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UserResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户工号
     */
    private String userCode;
    
    /**
     * 组织机构
     */
    private String organization;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 直接分配的角色列表
     */
    private List<RoleResponse> directRoles;
    
    /**
     * 所属用户组列表
     */
    private List<UserGroupResponse> userGroups;
    
    /**
     * 通过用户组继承的角色列表
     */
    private List<RoleResponse> inheritedRoles;
    
    /**
     * 所有菜单权限列表（直接分配 + 继承）
     */
    private List<MenuPermissionResponse> allMenuPermissions;
    
    /**
     * 权限来源详情
     */
    private List<PermissionSourceResponse> permissionSources;
}
