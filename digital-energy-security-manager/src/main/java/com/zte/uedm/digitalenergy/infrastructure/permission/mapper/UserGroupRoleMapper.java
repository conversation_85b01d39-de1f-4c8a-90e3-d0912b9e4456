package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserGroupRolePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户组角色关联Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Mapper
public interface UserGroupRoleMapper {
    
    /**
     * 批量插入用户组角色关联
     */
    int batchInsert(@Param("userGroupRoleList") List<UserGroupRolePO> userGroupRoleList);
    
    /**
     * 根据用户组ID删除关联
     */
    int deleteByUserGroupId(@Param("userGroupId") Long userGroupId);
    
    /**
     * 根据角色ID删除关联
     */
    int deleteByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据用户组ID查询角色ID列表
     */
    List<Long> selectRoleIdsByUserGroupId(@Param("userGroupId") Long userGroupId);
    
    /**
     * 根据角色ID查询用户组ID列表
     */
    List<Long> selectUserGroupIdsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 检查用户组角色关联是否存在
     */
    int existsByUserGroupIdAndRoleId(@Param("userGroupId") Long userGroupId, @Param("roleId") Long roleId);
    
    /**
     * 根据用户组ID和角色ID删除关联
     */
    int deleteByUserGroupIdAndRoleId(@Param("userGroupId") Long userGroupId, @Param("roleId") Long roleId);
}
