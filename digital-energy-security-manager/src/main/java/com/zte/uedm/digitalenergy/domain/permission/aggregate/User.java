package com.zte.uedm.digitalenergy.domain.permission.aggregate;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户聚合根
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class User {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户工号
     */
    private String userCode;
    
    /**
     * 组织机构
     */
    private String organization;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 直接分配的角色ID列表
     */
    private List<Long> roleIds;
    
    /**
     * 所属用户组ID列表
     */
    private List<Long> userGroupIds;
    
    /**
     * 用户直接分配角色的最大数量限制
     */
    public static final int MAX_DIRECT_ROLES = 10;
    
    /**
     * 创建用户
     */
    public static User createUser(String username, String userCode, String organization, String createBy) {
        validateUsername(username);
        validateUserCode(userCode);
        
        User user = new User();
        user.setUsername(username);
        user.setUserCode(userCode);
        user.setOrganization(organization);
        user.setCreateBy(createBy);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        return user;
    }
    
    /**
     * 更新用户信息
     */
    public void updateUser(String updateBy, List<Long> roleIds) {
        validateRoleLimit(roleIds);
        this.updateBy = updateBy;
        this.updateTime = LocalDateTime.now();
        this.roleIds = roleIds;
    }
    
    /**
     * 分配角色
     */
    public void assignRoles(List<Long> roleIds) {
        validateRoleLimit(roleIds);
        this.roleIds = roleIds;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 验证角色数量限制
     */
    private void validateRoleLimit(List<Long> roleIds) {
        if (roleIds != null && roleIds.size() > MAX_DIRECT_ROLES) {
            throw new IllegalArgumentException("User can have at most " + MAX_DIRECT_ROLES + " direct roles");
        }
    }
    
    /**
     * 验证用户名
     */
    public static void validateUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Username cannot be empty");
        }
        if (username.length() > 50) {
            throw new IllegalArgumentException("Username cannot exceed 50 characters");
        }
    }
    
    /**
     * 验证用户工号
     */
    public static void validateUserCode(String userCode) {
        if (userCode == null || userCode.trim().isEmpty()) {
            throw new IllegalArgumentException("User code cannot be empty");
        }
        if (userCode.length() > 50) {
            throw new IllegalArgumentException("User code cannot exceed 50 characters");
        }
    }
    
    /**
     * 获取直接分配的角色数量
     */
    public int getDirectRoleCount() {
        return roleIds != null ? roleIds.size() : 0;
    }
    
    /**
     * 判断是否可以添加更多角色
     */
    public boolean canAddMoreRoles() {
        return getDirectRoleCount() < MAX_DIRECT_ROLES;
    }
    
    /**
     * 判断是否有指定角色
     */
    public boolean hasRole(Long roleId) {
        return roleIds != null && roleIds.contains(roleId);
    }
}
