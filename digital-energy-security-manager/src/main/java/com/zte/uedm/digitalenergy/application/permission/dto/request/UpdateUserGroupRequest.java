package com.zte.uedm.digitalenergy.application.permission.dto.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 更新用户组请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UpdateUserGroupRequest {
    
    /**
     * 用户组ID
     */
    @NotNull(message = "User group ID cannot be null")
    private Long userGroupId;
    
    /**
     * 用户组描述
     */
    @Size(max = 200, message = "User group description cannot exceed 200 characters")
    private String groupDescription;
    
    /**
     * 关联的角色ID列表（最多10个）
     */
    private List<Long> roleIds;
    
    /**
     * 用户组成员ID列表
     */
    private List<Long> memberIds;
}
