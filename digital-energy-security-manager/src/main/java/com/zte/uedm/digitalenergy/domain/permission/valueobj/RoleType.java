package com.zte.uedm.digitalenergy.domain.permission.valueobj;

/**
 * 角色类型值对象
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public enum RoleType {
    
    /**
     * 默认角色（系统预置，不可删除）
     */
    DEFAULT("DEFAULT", "默认角色"),
    
    /**
     * 自定义角色（用户创建，可删除）
     */
    CUSTOM("CUSTOM", "自定义角色");
    
    private final String code;
    private final String description;
    
    RoleType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取角色类型
     */
    public static RoleType fromCode(String code) {
        for (RoleType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown role type code: " + code);
    }
    
    /**
     * 判断是否为默认角色
     */
    public boolean isDefault() {
        return this == DEFAULT;
    }
    
    /**
     * 判断是否为自定义角色
     */
    public boolean isCustom() {
        return this == CUSTOM;
    }
}
