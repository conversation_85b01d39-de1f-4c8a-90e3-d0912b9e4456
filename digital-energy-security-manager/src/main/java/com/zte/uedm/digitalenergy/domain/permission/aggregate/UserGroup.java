package com.zte.uedm.digitalenergy.domain.permission.aggregate;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户组聚合根
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UserGroup {
    
    /**
     * 用户组ID
     */
    private Long userGroupId;
    
    /**
     * 用户组名称
     */
    private String groupName;
    
    /**
     * 用户组描述
     */
    private String groupDescription;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 关联的角色ID列表
     */
    private List<Long> roleIds;
    
    /**
     * 用户组成员ID列表
     */
    private List<Long> memberIds;
    
    /**
     * 用户组角色的最大数量限制
     */
    public static final int MAX_GROUP_ROLES = 10;
    
    /**
     * 创建用户组
     */
    public static UserGroup createUserGroup(String groupName, String groupDescription, String createBy, 
                                          List<Long> roleIds, List<Long> memberIds) {
        validateGroupName(groupName);
        validateRoleLimit(roleIds);
        
        UserGroup userGroup = new UserGroup();
        userGroup.setGroupName(groupName);
        userGroup.setGroupDescription(groupDescription);
        userGroup.setCreateBy(createBy);
        userGroup.setCreateTime(LocalDateTime.now());
        userGroup.setUpdateTime(LocalDateTime.now());
        userGroup.setRoleIds(roleIds);
        userGroup.setMemberIds(memberIds);
        return userGroup;
    }
    
    /**
     * 更新用户组信息
     */
    public void updateUserGroup(String groupDescription, String updateBy, 
                               List<Long> roleIds, List<Long> memberIds) {
        validateRoleLimit(roleIds);
        this.groupDescription = groupDescription;
        this.updateBy = updateBy;
        this.updateTime = LocalDateTime.now();
        this.roleIds = roleIds;
        this.memberIds = memberIds;
    }
    
    /**
     * 添加成员
     */
    public void addMember(Long userId) {
        if (memberIds != null && !memberIds.contains(userId)) {
            memberIds.add(userId);
            this.updateTime = LocalDateTime.now();
        }
    }
    
    /**
     * 移除成员
     */
    public void removeMember(Long userId) {
        if (memberIds != null) {
            memberIds.remove(userId);
            this.updateTime = LocalDateTime.now();
        }
    }
    
    /**
     * 分配角色
     */
    public void assignRoles(List<Long> roleIds) {
        validateRoleLimit(roleIds);
        this.roleIds = roleIds;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 验证角色数量限制
     */
    private static void validateRoleLimit(List<Long> roleIds) {
        if (roleIds != null && roleIds.size() > MAX_GROUP_ROLES) {
            throw new IllegalArgumentException("User group can have at most " + MAX_GROUP_ROLES + " roles");
        }
    }
    
    /**
     * 验证用户组名称
     */
    public static void validateGroupName(String groupName) {
        if (groupName == null || groupName.trim().isEmpty()) {
            throw new IllegalArgumentException("Group name cannot be empty");
        }
        if (groupName.length() > 50) {
            throw new IllegalArgumentException("Group name cannot exceed 50 characters");
        }
    }
    
    /**
     * 验证用户组描述
     */
    public static void validateGroupDescription(String groupDescription) {
        if (groupDescription != null && groupDescription.length() > 500) {
            throw new IllegalArgumentException("Group description cannot exceed 500 characters");
        }
    }
    
    /**
     * 获取成员数量
     */
    public int getMemberCount() {
        return memberIds != null ? memberIds.size() : 0;
    }
    
    /**
     * 获取角色数量
     */
    public int getRoleCount() {
        return roleIds != null ? roleIds.size() : 0;
    }
    
    /**
     * 判断是否包含指定成员
     */
    public boolean hasMember(Long userId) {
        return memberIds != null && memberIds.contains(userId);
    }
    
    /**
     * 判断是否有指定角色
     */
    public boolean hasRole(Long roleId) {
        return roleIds != null && roleIds.contains(roleId);
    }
}
