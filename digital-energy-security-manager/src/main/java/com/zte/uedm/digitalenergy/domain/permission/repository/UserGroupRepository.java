package com.zte.uedm.digitalenergy.domain.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.aggregate.UserGroup;

import java.util.List;
import java.util.Optional;

/**
 * 用户组仓储接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public interface UserGroupRepository {
    
    /**
     * 保存用户组
     */
    UserGroup save(UserGroup userGroup);
    
    /**
     * 根据ID查找用户组
     */
    Optional<UserGroup> findById(Long userGroupId);
    
    /**
     * 根据用户组名称查找用户组
     */
    Optional<UserGroup> findByGroupName(String groupName);
    
    /**
     * 查找所有用户组
     */
    List<UserGroup> findAll();
    
    /**
     * 分页查询用户组
     */
    List<UserGroup> findByPage(int pageNum, int pageSize, String groupName);
    
    /**
     * 统计用户组总数
     */
    long count();
    
    /**
     * 删除用户组
     */
    void deleteById(Long userGroupId);
    
    /**
     * 批量删除用户组
     */
    void deleteByIds(List<Long> userGroupIds);
    
    /**
     * 检查用户组名称是否存在
     */
    boolean existsByGroupName(String groupName);
    
    /**
     * 为用户组分配角色
     */
    void assignRolesToUserGroup(Long userGroupId, List<Long> roleIds);
    
    /**
     * 移除用户组的角色
     */
    void removeRolesFromUserGroup(Long userGroupId);
    
    /**
     * 获取用户组的角色
     */
    List<Long> findRoleIdsByUserGroupId(Long userGroupId);
    
    /**
     * 为用户组添加成员
     */
    void addMembersToUserGroup(Long userGroupId, List<Long> userIds);
    
    /**
     * 从用户组移除成员
     */
    void removeMembersFromUserGroup(Long userGroupId, List<Long> userIds);
    
    /**
     * 移除用户组的所有成员
     */
    void removeAllMembersFromUserGroup(Long userGroupId);
    
    /**
     * 获取用户组的成员
     */
    List<Long> findMemberIdsByUserGroupId(Long userGroupId);
    
    /**
     * 获取用户组的所有菜单权限
     */
    List<String> findAllMenuIdsByUserGroupId(Long userGroupId);
    
    /**
     * 根据角色ID查找用户组
     */
    List<UserGroup> findByRoleId(Long roleId);
    
    /**
     * 根据用户ID查找用户组
     */
    List<UserGroup> findByUserId(Long userId);
    
    /**
     * 检查用户是否属于用户组
     */
    boolean isUserInGroup(Long userGroupId, Long userId);
    
    /**
     * 统计用户组成员数量
     */
    int countMembersByUserGroupId(Long userGroupId);
}
