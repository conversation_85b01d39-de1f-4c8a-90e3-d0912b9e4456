package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserGroupMemberPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户组成员关联Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Mapper
public interface UserGroupMemberMapper {
    
    /**
     * 批量插入用户组成员关联
     */
    int batchInsert(@Param("userGroupMemberList") List<UserGroupMemberPO> userGroupMemberList);
    
    /**
     * 根据用户组ID删除关联
     */
    int deleteByUserGroupId(@Param("userGroupId") Long userGroupId);
    
    /**
     * 根据用户ID删除关联
     */
    int deleteByUserId(@Param("userId") Long userId);
    
    /**
     * 根据用户组ID查询用户ID列表
     */
    List<Long> selectUserIdsByUserGroupId(@Param("userGroupId") Long userGroupId);
    
    /**
     * 根据用户ID查询用户组ID列表
     */
    List<Long> selectUserGroupIdsByUserId(@Param("userId") Long userId);
    
    /**
     * 检查用户组成员关联是否存在
     */
    int existsByUserGroupIdAndUserId(@Param("userGroupId") Long userGroupId, @Param("userId") Long userId);
    
    /**
     * 根据用户组ID和用户ID删除关联
     */
    int deleteByUserGroupIdAndUserId(@Param("userGroupId") Long userGroupId, @Param("userId") Long userId);
    
    /**
     * 统计用户组成员数量
     */
    int countByUserGroupId(@Param("userGroupId") Long userGroupId);
}
