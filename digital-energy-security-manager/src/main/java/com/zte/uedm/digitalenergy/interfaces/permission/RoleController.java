package com.zte.uedm.digitalenergy.interfaces.permission;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.digitalenergy.application.permission.dto.request.CreateRoleRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.RoleQueryRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UpdateRoleRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.response.MenuPermissionResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.RoleResponse;
import com.zte.uedm.digitalenergy.application.permission.service.RoleApplicationService;
import com.zte.uedm.digitalenergy.common.response.ResponseBean;
import com.zte.uedm.digitalenergy.common.response.ResponseBeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色管理控制器
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@Api(tags = "角色管理")
@RestController
@RequestMapping("/api/security-manager/v1/permission/roles")
@Validated
public class RoleController {
    
    @Autowired
    private RoleApplicationService roleApplicationService;
    
    @ApiOperation("创建角色")
    @PostMapping
    public ResponseBean<RoleResponse> createRole(
            @Valid @RequestBody CreateRoleRequest request,
            @RequestHeader(value = "X-User-Code", defaultValue = "system") String currentUser) {
        try {
            log.info("Create role request: {} by user: {}", request.getRoleName(), currentUser);
            RoleResponse response = roleApplicationService.createRole(request, currentUser);
            return ResponseBeanUtils.success("Role created successfully", response);
        } catch (IllegalArgumentException e) {
            log.warn("Create role failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("Create role failed: {}", e.getMessage());
            return ResponseBeanUtils.error(1003, e.getMessage());
        } catch (Exception e) {
            log.error("Create role error", e);
            return ResponseBeanUtils.systemError("Failed to create role");
        }
    }
    
    @ApiOperation("更新角色")
    @PutMapping("/{roleId}")
    public ResponseBean<RoleResponse> updateRole(
            @ApiParam("角色ID") @PathVariable @NotNull Long roleId,
            @Valid @RequestBody UpdateRoleRequest request,
            @RequestHeader(value = "X-User-Code", defaultValue = "system") String currentUser) {
        try {
            request.setRoleId(roleId);
            log.info("Update role request: {} by user: {}", roleId, currentUser);
            RoleResponse response = roleApplicationService.updateRole(request, currentUser);
            return ResponseBeanUtils.success("Role updated successfully", response);
        } catch (IllegalArgumentException e) {
            log.warn("Update role failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("Update role failed: {}", e.getMessage());
            return ResponseBeanUtils.error(1003, e.getMessage());
        } catch (Exception e) {
            log.error("Update role error", e);
            return ResponseBeanUtils.systemError("Failed to update role");
        }
    }
    
    @ApiOperation("删除角色")
    @DeleteMapping("/{roleId}")
    public ResponseBean<Void> deleteRole(
            @ApiParam("角色ID") @PathVariable @NotNull Long roleId) {
        try {
            log.info("Delete role request: {}", roleId);
            roleApplicationService.deleteRole(roleId);
            return ResponseBeanUtils.success("Role deleted successfully");
        } catch (IllegalArgumentException e) {
            log.warn("Delete role failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("Delete role failed: {}", e.getMessage());
            return ResponseBeanUtils.error(1003, e.getMessage());
        } catch (Exception e) {
            log.error("Delete role error", e);
            return ResponseBeanUtils.systemError("Failed to delete role");
        }
    }
    
    @ApiOperation("批量删除角色")
    @DeleteMapping("/batch")
    public ResponseBean<Void> deleteRoles(
            @ApiParam("角色ID列表") @RequestBody @NotEmpty List<Long> roleIds) {
        try {
            log.info("Batch delete roles request: {}", roleIds);
            roleApplicationService.deleteRoles(roleIds);
            return ResponseBeanUtils.success("Roles deleted successfully");
        } catch (IllegalArgumentException e) {
            log.warn("Batch delete roles failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("Batch delete roles failed: {}", e.getMessage());
            return ResponseBeanUtils.error(1003, e.getMessage());
        } catch (Exception e) {
            log.error("Batch delete roles error", e);
            return ResponseBeanUtils.systemError("Failed to delete roles");
        }
    }
    
    @ApiOperation("根据ID查询角色详情")
    @GetMapping("/{roleId}")
    public ResponseBean<RoleResponse> getRoleById(
            @ApiParam("角色ID") @PathVariable @NotNull Long roleId) {
        try {
            log.info("Get role by ID request: {}", roleId);
            RoleResponse response = roleApplicationService.getRoleById(roleId);
            return ResponseBeanUtils.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("Get role failed: {}", e.getMessage());
            return ResponseBeanUtils.notFound(e.getMessage());
        } catch (Exception e) {
            log.error("Get role error", e);
            return ResponseBeanUtils.systemError("Failed to get role");
        }
    }
    
    @ApiOperation("分页查询角色列表")
    @GetMapping
    public ResponseBean<PageInfo<RoleResponse>> getRolesByPage(@Valid RoleQueryRequest request) {
        try {
            log.info("Get roles by page request: {}", request);
            PageInfo<RoleResponse> response = roleApplicationService.getRolesByPage(request);
            return ResponseBeanUtils.success(response);
        } catch (Exception e) {
            log.error("Get roles by page error", e);
            return ResponseBeanUtils.systemError("Failed to get roles");
        }
    }
    
    @ApiOperation("获取所有角色列表")
    @GetMapping("/all")
    public ResponseBean<List<RoleResponse>> getAllRoles() {
        try {
            log.info("Get all roles request");
            List<RoleResponse> response = roleApplicationService.getAllRoles();
            return ResponseBeanUtils.success(response);
        } catch (Exception e) {
            log.error("Get all roles error", e);
            return ResponseBeanUtils.systemError("Failed to get all roles");
        }
    }
    
    @ApiOperation("获取角色的菜单权限")
    @GetMapping("/{roleId}/permissions")
    public ResponseBean<List<MenuPermissionResponse>> getRoleMenuPermissions(
            @ApiParam("角色ID") @PathVariable @NotNull Long roleId) {
        try {
            log.info("Get role menu permissions request: {}", roleId);
            List<MenuPermissionResponse> response = roleApplicationService.getRoleMenuPermissions(roleId);
            return ResponseBeanUtils.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("Get role menu permissions failed: {}", e.getMessage());
            return ResponseBeanUtils.notFound(e.getMessage());
        } catch (Exception e) {
            log.error("Get role menu permissions error", e);
            return ResponseBeanUtils.systemError("Failed to get role menu permissions");
        }
    }
    
    @ApiOperation("获取所有菜单权限树")
    @GetMapping("/permissions/all")
    public ResponseBean<List<MenuPermissionResponse>> getAllMenuPermissions() {
        try {
            log.info("Get all menu permissions request");
            List<MenuPermissionResponse> response = roleApplicationService.getAllMenuPermissions();
            return ResponseBeanUtils.success(response);
        } catch (Exception e) {
            log.error("Get all menu permissions error", e);
            return ResponseBeanUtils.systemError("Failed to get all menu permissions");
        }
    }
}
