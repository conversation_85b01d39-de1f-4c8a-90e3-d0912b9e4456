package com.zte.uedm.digitalenergy.infrastructure.permission.repository;

import com.github.pagehelper.PageHelper;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.User;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserRepository;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserRoleMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户仓储实现类
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@Repository
public class UserRepositoryImpl implements UserRepository {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Override
    @Transactional
    public User save(User user) {
        if (user.getUserId() == null) {
            // 新增用户
            UserPO userPO = convertToUserPO(user);
            userMapper.insert(userPO);
            user.setUserId(userPO.getUserId());
            log.info("Created new user: {}", user.getUserCode());
            
            // 保存用户角色关联
            if (user.getRoleIds() != null && !user.getRoleIds().isEmpty()) {
                assignRolesToUser(user.getUserId(), user.getRoleIds());
            }
        } else {
            // 更新用户
            UserPO userPO = convertToUserPO(user);
            userMapper.update(userPO);
            log.info("Updated user: {}", user.getUserCode());
            
            // 更新用户角色关联
            userRoleMapper.deleteByUserId(user.getUserId());
            if (user.getRoleIds() != null && !user.getRoleIds().isEmpty()) {
                assignRolesToUser(user.getUserId(), user.getRoleIds());
            }
        }
        return user;
    }
    
    @Override
    public Optional<User> findById(Long userId) {
        UserPO userPO = userMapper.selectById(userId);
        if (userPO == null) {
            return Optional.empty();
        }
        User user = convertToUser(userPO);
        // 加载用户角色
        List<Long> roleIds = userRoleMapper.selectRoleIdsByUserId(userId);
        user.setRoleIds(roleIds);
        return Optional.of(user);
    }
    
    @Override
    public Optional<User> findByUserCode(String userCode) {
        UserPO userPO = userMapper.selectByUserCode(userCode);
        if (userPO == null) {
            return Optional.empty();
        }
        User user = convertToUser(userPO);
        // 加载用户角色
        List<Long> roleIds = userRoleMapper.selectRoleIdsByUserId(user.getUserId());
        user.setRoleIds(roleIds);
        return Optional.of(user);
    }
    
    @Override
    public Optional<User> findByUsername(String username) {
        UserPO userPO = userMapper.selectByUsername(username);
        if (userPO == null) {
            return Optional.empty();
        }
        User user = convertToUser(userPO);
        // 加载用户角色
        List<Long> roleIds = userRoleMapper.selectRoleIdsByUserId(user.getUserId());
        user.setRoleIds(roleIds);
        return Optional.of(user);
    }
    
    @Override
    public List<User> findAll() {
        List<UserPO> userPOList = userMapper.selectAll();
        return userPOList.stream()
                .map(this::convertToUser)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<User> findByPage(int pageNum, int pageSize, String username, String userCode, String organization) {
        PageHelper.startPage(pageNum, pageSize);
        List<UserPO> userPOList = userMapper.selectByPage(username, userCode, organization);
        return userPOList.stream()
                .map(this::convertToUser)
                .collect(Collectors.toList());
    }
    
    @Override
    public long count() {
        return userMapper.count();
    }
    
    @Override
    @Transactional
    public void deleteById(Long userId) {
        // 删除用户角色关联
        userRoleMapper.deleteByUserId(userId);
        // 删除用户组成员关联
        userMapper.removeUserFromAllGroups(userId);
        // 删除用户
        userMapper.deleteById(userId);
        log.info("Deleted user with ID: {}", userId);
    }
    
    @Override
    @Transactional
    public void deleteByIds(List<Long> userIds) {
        for (Long userId : userIds) {
            userRoleMapper.deleteByUserId(userId);
            userMapper.removeUserFromAllGroups(userId);
        }
        userMapper.deleteByIds(userIds);
        log.info("Deleted {} users", userIds.size());
    }
    
    @Override
    public boolean existsByUserCode(String userCode) {
        return userMapper.existsByUserCode(userCode) > 0;
    }
    
    @Override
    public boolean existsById(Long userId) {
        return userMapper.existsById(userId) > 0;
    }
    
    @Override
    @Transactional
    public void assignRolesToUser(Long userId, List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return;
        }
        userMapper.assignRolesToUser(userId, roleIds);
        log.info("Assigned {} roles to user {}", roleIds.size(), userId);
    }
    
    @Override
    @Transactional
    public void removeRolesFromUser(Long userId) {
        userMapper.removeRolesFromUser(userId);
        log.info("Removed all roles from user {}", userId);
    }
    
    @Override
    public List<Long> findDirectRoleIdsByUserId(Long userId) {
        return userMapper.findDirectRoleIdsByUserId(userId);
    }
    
    @Override
    public List<Long> findUserGroupIdsByUserId(Long userId) {
        return userMapper.findUserGroupIdsByUserId(userId);
    }
    
    @Override
    public List<Long> findAllRoleIdsByUserId(Long userId) {
        return userMapper.findAllRoleIdsByUserId(userId);
    }
    
    @Override
    public List<String> findAllMenuIdsByUserId(Long userId) {
        return userMapper.findAllMenuIdsByUserId(userId);
    }
    
    @Override
    public List<User> findByRoleId(Long roleId) {
        List<UserPO> userPOList = userMapper.findByRoleId(roleId);
        return userPOList.stream()
                .map(this::convertToUser)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<User> findByUserGroupId(Long userGroupId) {
        List<UserPO> userPOList = userMapper.findByUserGroupId(userGroupId);
        return userPOList.stream()
                .map(this::convertToUser)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public void removeUserFromAllGroups(Long userId) {
        userMapper.removeUserFromAllGroups(userId);
        log.info("Removed user {} from all groups", userId);
    }
    
    /**
     * 转换为用户PO
     */
    private UserPO convertToUserPO(User user) {
        UserPO userPO = new UserPO();
        userPO.setUserId(user.getUserId());
        userPO.setUsername(user.getUsername());
        userPO.setUserCode(user.getUserCode());
        userPO.setOrganization(user.getOrganization());
        userPO.setCreateTime(user.getCreateTime());
        userPO.setCreateBy(user.getCreateBy());
        userPO.setUpdateTime(user.getUpdateTime());
        userPO.setUpdateBy(user.getUpdateBy());
        return userPO;
    }
    
    /**
     * 转换为用户领域对象
     */
    private User convertToUser(UserPO userPO) {
        User user = new User();
        user.setUserId(userPO.getUserId());
        user.setUsername(userPO.getUsername());
        user.setUserCode(userPO.getUserCode());
        user.setOrganization(userPO.getOrganization());
        user.setCreateTime(userPO.getCreateTime());
        user.setCreateBy(userPO.getCreateBy());
        user.setUpdateTime(userPO.getUpdateTime());
        user.setUpdateBy(userPO.getUpdateBy());
        return user;
    }
}
