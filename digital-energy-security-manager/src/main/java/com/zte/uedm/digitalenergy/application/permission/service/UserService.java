package com.zte.uedm.digitalenergy.application.permission.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.digitalenergy.application.permission.dto.request.CreateUserRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UpdateUserRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UserQueryRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.response.MenuPermissionResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.PermissionSourceResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.UserResponse;

import java.util.List;

/**
 * 用户应用服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public interface UserService {
    
    /**
     * 创建用户
     */
    UserResponse createUser(CreateUserRequest request, String currentUser);
    
    /**
     * 更新用户
     */
    UserResponse updateUser(UpdateUserRequest request, String currentUser);
    
    /**
     * 删除用户
     */
    void deleteUser(Long userId);

    /**
     * 批量删除用户
     */
    void deleteUsers(List<Long> userIds);

    /**
     * 根据ID查询用户详情
     */
    UserResponse getUserById(Long userId);
    
    /**
     * 分页查询用户列表
     */
    PageInfo<UserResponse> getUsersByPage(UserQueryRequest request);
    
    /**
     * 获取用户权限详情
     */
    List<PermissionSourceResponse> getUserPermissionDetails(Long userId);
    
    /**
     * 获取用户的菜单权限树
     */
    List<MenuPermissionResponse> getUserMenuPermissions(Long userId);
}
