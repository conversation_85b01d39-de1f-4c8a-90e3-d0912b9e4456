package com.zte.uedm.digitalenergy.domain.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.aggregate.Menu;
import com.zte.uedm.digitalenergy.domain.permission.valueobj.MenuLevel;

import java.util.List;
import java.util.Optional;

/**
 * 菜单仓储接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public interface MenuRepository {
    
    /**
     * 保存菜单
     */
    Menu save(Menu menu);
    
    /**
     * 根据ID查找菜单
     */
    Optional<Menu> findById(String menuId);
    
    /**
     * 根据菜单名称查找菜单
     */
    Optional<Menu> findByMenuName(String menuName);
    
    /**
     * 查找所有菜单
     */
    List<Menu> findAll();
    
    /**
     * 根据层级查找菜单
     */
    List<Menu> findByMenuLevel(MenuLevel menuLevel);
    
    /**
     * 根据父菜单ID查找子菜单
     */
    List<Menu> findByParentId(Long parentId);
    
    /**
     * 获取菜单树结构
     */
    List<Menu> findMenuTree();
    
    /**
     * 获取指定菜单的子菜单树
     */
    List<Menu> findSubMenuTree(String parentMenuId);
    
    /**
     * 删除菜单
     */
    void deleteById(String menuId);
    
    /**
     * 批量删除菜单
     */
    void deleteByIds(List<String> menuIds);
    
    /**
     * 检查菜单ID是否存在
     */
    boolean existsById(String menuId);
    
    /**
     * 检查菜单名称是否存在
     */
    boolean existsByMenuName(String menuName);
    
    /**
     * 根据菜单ID列表查找菜单
     */
    List<Menu> findByIds(List<String> menuIds);
    
    /**
     * 获取顶级菜单（第一层）
     */
    List<Menu> findTopLevelMenus();
    
    /**
     * 根据路径查找菜单
     */
    Optional<Menu> findByMenuPath(String menuPath);
    
    /**
     * 检查菜单是否有子菜单
     */
    boolean hasChildren(String menuId);
    
    /**
     * 获取菜单的所有子菜单ID（递归）
     */
    List<String> findAllChildMenuIds(String menuId);
    
    /**
     * 根据排序号排序查找菜单
     */
    List<Menu> findByParentIdOrderBySortOrder(Long parentId);
}
