package com.zte.uedm.digitalenergy.domain.permission.valueobj;

import lombok.Value;

/**
 * 菜单层级值对象
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Value
public class MenuLevel {
    
    /**
     * 层级数值
     */
    int level;
    
    /**
     * 根菜单层级
     */
    public static final MenuLevel ROOT = new MenuLevel(0);
    
    /**
     * 第一层菜单
     */
    public static final MenuLevel FIRST = new MenuLevel(1);
    
    /**
     * 第二层菜单
     */
    public static final MenuLevel SECOND = new MenuLevel(2);
    
    /**
     * 第三层菜单
     */
    public static final MenuLevel THIRD = new MenuLevel(3);
    
    /**
     * 第四层菜单
     */
    public static final MenuLevel FOURTH = new MenuLevel(4);
    
    public MenuLevel(int level) {
        if (level < 0) {
            throw new IllegalArgumentException("Menu level cannot be negative");
        }
        this.level = level;
    }
    
    /**
     * 判断是否为根菜单
     */
    public boolean isRoot() {
        return level == 0;
    }
    
    /**
     * 判断是否为顶级菜单（第一层）
     */
    public boolean isTopLevel() {
        return level == 1;
    }
    
    /**
     * 获取下一层级
     */
    public MenuLevel nextLevel() {
        return new MenuLevel(level + 1);
    }
    
    /**
     * 获取上一层级
     */
    public MenuLevel previousLevel() {
        if (level <= 0) {
            throw new IllegalStateException("Cannot get previous level for root menu");
        }
        return new MenuLevel(level - 1);
    }
    
    /**
     * 比较层级
     */
    public boolean isHigherThan(MenuLevel other) {
        return this.level < other.level;
    }
    
    public boolean isLowerThan(MenuLevel other) {
        return this.level > other.level;
    }
    
    public boolean isSameLevel(MenuLevel other) {
        return this.level == other.level;
    }
}
