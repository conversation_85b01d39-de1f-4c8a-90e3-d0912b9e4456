package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.zte.uedm.digitalenergy.infrastructure.permission.po.RolePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Mapper
public interface RoleMapper {
    
    /**
     * 插入角色
     */
    int insert(RolePO rolePO);
    
    /**
     * 更新角色
     */
    int update(RolePO rolePO);
    
    /**
     * 根据ID查询角色
     */
    RolePO selectById(@Param("roleId") Long roleId);
    
    /**
     * 根据角色名称查询角色
     */
    RolePO selectByRoleName(@Param("roleName") String roleName);
    
    /**
     * 根据角色类型查询角色列表
     */
    List<RolePO> selectByRoleType(@Param("roleType") String roleType);
    
    /**
     * 查询所有角色
     */
    List<RolePO> selectAll();
    
    /**
     * 分页查询角色
     */
    List<RolePO> selectByPage(@Param("roleName") String roleName, @Param("roleType") String roleType);
    
    /**
     * 统计角色总数
     */
    long count();
    
    /**
     * 统计指定类型的角色数量
     */
    long countByRoleType(@Param("roleType") String roleType);
    
    /**
     * 根据ID删除角色
     */
    int deleteById(@Param("roleId") Long roleId);
    
    /**
     * 批量删除角色
     */
    int deleteByIds(@Param("roleIds") List<Long> roleIds);
    
    /**
     * 检查角色名称是否存在
     */
    int existsByRoleName(@Param("roleName") String roleName);
    
    /**
     * 检查角色是否被用户使用
     */
    int countUsersByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 检查角色是否被用户组使用
     */
    int countUserGroupsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 获取使用指定角色的用户ID列表
     */
    List<Long> selectUserIdsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 获取使用指定角色的用户组ID列表
     */
    List<Long> selectUserGroupIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 为角色分配菜单权限
     */
    int assignMenusToRole(@Param("roleId") Long roleId, @Param("menuIds") List<String> menuIds);

    /**
     * 移除角色的菜单权限
     */
    int removeMenusFromRole(@Param("roleId") Long roleId);

    /**
     * 获取角色的菜单权限
     */
    List<String> selectMenuIdsByRoleId(@Param("roleId") Long roleId);
}
