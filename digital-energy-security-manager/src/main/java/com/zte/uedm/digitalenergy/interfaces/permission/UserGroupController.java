package com.zte.uedm.digitalenergy.interfaces.permission;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.digitalenergy.application.permission.dto.request.CreateUserGroupRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UpdateUserGroupRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UserGroupQueryRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.response.MenuPermissionResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.UserGroupResponse;
import com.zte.uedm.digitalenergy.application.permission.service.UserGroupApplicationService;
import com.zte.uedm.digitalenergy.common.response.ResponseBean;
import com.zte.uedm.digitalenergy.common.response.ResponseBeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户组管理控制器
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@RestController
@RequestMapping("/api/security-manager/v1/permission/user-groups")
@Api(tags = "用户组管理")
public class UserGroupController {
    
    @Autowired
    private UserGroupApplicationService userGroupApplicationService;
    
    @ApiOperation("创建用户组")
    @PostMapping
    public ResponseBean<UserGroupResponse> createUserGroup(
            @Valid @RequestBody CreateUserGroupRequest request,
            @RequestHeader(value = "X-User-Code", defaultValue = "system") String currentUser) {
        try {
            log.info("Create user group request: {} by user: {}", request.getGroupName(), currentUser);
            UserGroupResponse response = userGroupApplicationService.createUserGroup(request, currentUser);
            return ResponseBeanUtils.success("User group created successfully", response);
        } catch (IllegalArgumentException e) {
            log.warn("Create user group failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("Create user group failed: {}", e.getMessage());
            return ResponseBeanUtils.error(1003, e.getMessage());
        } catch (Exception e) {
            log.error("Create user group error", e);
            return ResponseBeanUtils.systemError("Failed to create user group");
        }
    }
    
    @ApiOperation("更新用户组")
    @PutMapping("/{userGroupId}")
    public ResponseBean<UserGroupResponse> updateUserGroup(
            @ApiParam("用户组ID") @PathVariable @NotNull Long userGroupId,
            @Valid @RequestBody UpdateUserGroupRequest request,
            @RequestHeader(value = "X-User-Code", defaultValue = "system") String currentUser) {
        try {
            request.setUserGroupId(userGroupId);
            log.info("Update user group request: {} by user: {}", userGroupId, currentUser);
            UserGroupResponse response = userGroupApplicationService.updateUserGroup(request, currentUser);
            return ResponseBeanUtils.success("User group updated successfully", response);
        } catch (IllegalArgumentException e) {
            log.warn("Update user group failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("Update user group failed: {}", e.getMessage());
            return ResponseBeanUtils.error(1003, e.getMessage());
        } catch (Exception e) {
            log.error("Update user group error", e);
            return ResponseBeanUtils.systemError("Failed to update user group");
        }
    }
    
    @ApiOperation("删除用户组")
    @DeleteMapping("/{userGroupId}")
    public ResponseBean<Void> deleteUserGroup(
            @ApiParam("用户组ID") @PathVariable @NotNull Long userGroupId) {
        try {
            log.info("Delete user group request: {}", userGroupId);
            userGroupApplicationService.deleteUserGroup(userGroupId);
            return ResponseBeanUtils.success("User group deleted successfully");
        } catch (IllegalArgumentException e) {
            log.warn("Delete user group failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (Exception e) {
            log.error("Delete user group error", e);
            return ResponseBeanUtils.systemError("Failed to delete user group");
        }
    }
    
    @ApiOperation("批量删除用户组")
    @DeleteMapping
    public ResponseBean<Void> deleteUserGroups(
            @ApiParam("用户组ID列表") @RequestParam List<Long> userGroupIds) {
        try {
            log.info("Batch delete user groups request: {}", userGroupIds);
            userGroupApplicationService.deleteUserGroups(userGroupIds);
            return ResponseBeanUtils.success("User groups deleted successfully");
        } catch (IllegalArgumentException e) {
            log.warn("Batch delete user groups failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (Exception e) {
            log.error("Batch delete user groups error", e);
            return ResponseBeanUtils.systemError("Failed to delete user groups");
        }
    }
    
    @ApiOperation("根据ID查询用户组详情")
    @GetMapping("/{userGroupId}")
    public ResponseBean<UserGroupResponse> getUserGroupById(
            @ApiParam("用户组ID") @PathVariable @NotNull Long userGroupId) {
        try {
            log.info("Get user group by id request: {}", userGroupId);
            UserGroupResponse response = userGroupApplicationService.getUserGroupById(userGroupId);
            return ResponseBeanUtils.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("Get user group by id failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (Exception e) {
            log.error("Get user group by id error", e);
            return ResponseBeanUtils.systemError("Failed to get user group");
        }
    }
    
    @ApiOperation("分页查询用户组列表")
    @GetMapping
    public ResponseBean<PageInfo<UserGroupResponse>> getUserGroupsByPage(@Valid UserGroupQueryRequest request) {
        try {
            log.info("Get user groups by page request: {}", request);
            PageInfo<UserGroupResponse> response = userGroupApplicationService.getUserGroupsByPage(request);
            return ResponseBeanUtils.success(response);
        } catch (Exception e) {
            log.error("Get user groups by page error", e);
            return ResponseBeanUtils.systemError("Failed to get user groups");
        }
    }
    
    @ApiOperation("获取所有用户组列表")
    @GetMapping("/all")
    public ResponseBean<List<UserGroupResponse>> getAllUserGroups() {
        try {
            log.info("Get all user groups request");
            List<UserGroupResponse> response = userGroupApplicationService.getAllUserGroups();
            return ResponseBeanUtils.success(response);
        } catch (Exception e) {
            log.error("Get all user groups error", e);
            return ResponseBeanUtils.systemError("Failed to get all user groups");
        }
    }
    

    
    @ApiOperation("获取用户组的菜单权限树")
    @GetMapping("/{userGroupId}/menu-permissions")
    public ResponseBean<List<MenuPermissionResponse>> getUserGroupMenuPermissions(
            @ApiParam("用户组ID") @PathVariable @NotNull Long userGroupId) {
        try {
            log.info("Get user group menu permissions request: {}", userGroupId);
            List<MenuPermissionResponse> response = userGroupApplicationService.getUserGroupMenuPermissions(userGroupId);
            return ResponseBeanUtils.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("Get user group menu permissions failed: {}", e.getMessage());
            return ResponseBeanUtils.paramError(e.getMessage());
        } catch (Exception e) {
            log.error("Get user group menu permissions error", e);
            return ResponseBeanUtils.systemError("Failed to get user group menu permissions");
        }
    }
}
