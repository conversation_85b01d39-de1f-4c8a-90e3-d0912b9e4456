package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户角色关联持久化对象
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Data
public class UserRolePO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 角色ID
     */
    private Long roleId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
