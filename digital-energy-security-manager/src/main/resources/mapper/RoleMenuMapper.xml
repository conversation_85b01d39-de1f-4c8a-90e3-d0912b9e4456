<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.RoleMenuMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.zte.uedm.digitalenergy.infrastructure.permission.po.RoleMenuPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="menu_id" property="menuId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入角色菜单关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_role_menu (role_id, menu_id, create_time)
        VALUES
        <foreach collection="roleMenuList" item="item" separator=",">
            (#{item.roleId}, #{item.menuId}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 根据角色ID删除关联 -->
    <delete id="deleteByRoleId" parameterType="java.lang.Long">
        DELETE FROM t_role_menu WHERE role_id = #{roleId}
    </delete>

    <!-- 根据菜单ID删除关联 -->
    <delete id="deleteByMenuId" parameterType="java.lang.String">
        DELETE FROM t_role_menu WHERE menu_id = #{menuId}
    </delete>

    <!-- 根据角色ID查询菜单ID列表 -->
    <select id="selectMenuIdsByRoleId" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT menu_id FROM t_role_menu WHERE role_id = #{roleId}
    </select>

    <!-- 根据菜单ID查询角色ID列表 -->
    <select id="selectRoleIdsByMenuId" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT role_id FROM t_role_menu WHERE menu_id = #{menuId}
    </select>

    <!-- 检查角色菜单关联是否存在 -->
    <select id="existsByRoleIdAndMenuId" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM t_role_menu 
        WHERE role_id = #{roleId} AND menu_id = #{menuId}
    </select>

</mapper>
