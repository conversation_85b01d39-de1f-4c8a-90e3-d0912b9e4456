<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.RoleMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.zte.uedm.digitalenergy.infrastructure.permission.po.RolePO">
        <id column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="role_description" property="roleDescription" jdbcType="VARCHAR"/>
        <result column="role_type" property="roleType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        role_id, role_name, role_description, role_type, create_time, create_by, update_time, update_by
    </sql>

    <!-- 插入角色 -->
    <insert id="insert" parameterType="com.zte.uedm.digitalenergy.infrastructure.permission.po.RolePO" useGeneratedKeys="true" keyProperty="roleId">
        INSERT INTO t_role (role_name, role_description, role_type, create_time, create_by, update_time, update_by)
        VALUES (#{roleName}, #{roleDescription}, #{roleType}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy})
    </insert>

    <!-- 更新角色 -->
    <update id="update" parameterType="com.zte.uedm.digitalenergy.infrastructure.permission.po.RolePO">
        UPDATE t_role
        SET role_description = #{roleDescription},
            update_time = #{updateTime},
            update_by = #{updateBy}
        WHERE role_id = #{roleId}
    </update>

    <!-- 根据ID查询角色 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role
        WHERE role_id = #{roleId}
    </select>

    <!-- 根据角色名称查询角色 -->
    <select id="selectByRoleName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role
        WHERE role_name = #{roleName}
    </select>

    <!-- 根据角色类型查询角色列表 -->
    <select id="selectByRoleType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role
        WHERE role_type = #{roleType}
        ORDER BY create_time DESC
    </select>

    <!-- 查询所有角色 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role
        ORDER BY role_type, create_time DESC
    </select>

    <!-- 分页查询角色 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_role
        <where>
            <if test="roleName != null and roleName != ''">
                AND role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleType != null and roleType != ''">
                AND role_type = #{roleType}
            </if>
        </where>
        ORDER BY role_type, create_time DESC
    </select>

    <!-- 统计角色总数 -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(1) FROM t_role
    </select>

    <!-- 统计指定类型的角色数量 -->
    <select id="countByRoleType" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT COUNT(1) FROM t_role WHERE role_type = #{roleType}
    </select>

    <!-- 根据ID删除角色 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM t_role WHERE role_id = #{roleId}
    </delete>

    <!-- 批量删除角色 -->
    <delete id="deleteByIds">
        DELETE FROM t_role WHERE role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <!-- 检查角色名称是否存在 -->
    <select id="existsByRoleName" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM t_role WHERE role_name = #{roleName}
    </select>

    <!-- 检查角色是否被用户使用 -->
    <select id="countUsersByRoleId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM t_user_role WHERE role_id = #{roleId}
    </select>

    <!-- 检查角色是否被用户组使用 -->
    <select id="countUserGroupsByRoleId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM t_user_group_role WHERE role_id = #{roleId}
    </select>

    <!-- 获取使用指定角色的用户ID列表 -->
    <select id="selectUserIdsByRoleId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT user_id FROM t_user_role WHERE role_id = #{roleId}
    </select>

    <!-- 获取使用指定角色的用户组ID列表 -->
    <select id="selectUserGroupIdsByRoleId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT user_group_id FROM t_user_group_role WHERE role_id = #{roleId}
    </select>

    <!-- 为角色分配菜单权限 -->
    <insert id="assignMenusToRole">
        INSERT INTO t_role_menu (role_id, menu_id, create_time)
        VALUES
        <foreach collection="menuIds" item="menuId" separator=",">
            (#{roleId}, #{menuId}, NOW())
        </foreach>
    </insert>

    <!-- 移除角色的菜单权限 -->
    <delete id="removeMenusFromRole" parameterType="long">
        DELETE FROM t_role_menu WHERE role_id = #{roleId}
    </delete>

    <!-- 获取角色的菜单权限 -->
    <select id="selectMenuIdsByRoleId" parameterType="long" resultType="string">
        SELECT menu_id FROM t_role_menu WHERE role_id = #{roleId}
    </select>

</mapper>
