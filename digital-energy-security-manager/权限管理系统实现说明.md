# 权限管理系统实现说明

## 项目概述

基于DDD（领域驱动设计）架构实现的完整权限管理系统，支持角色管理、用户管理、用户组管理等核心功能。

## 技术栈

- **后端框架**: Java 1.8 + Spring Boot + MyBatis
- **数据库**: PostgreSQL
- **缓存**: Redis
- **消息队列**: Kafka
- **架构模式**: DDD（领域驱动设计）
- **工具库**: Lombok + MapStruct + PageHelper

## 架构设计

### DDD分层架构

```
com.zte.uedm.digitalenergy
├── common/                    # 公共层
│   ├── response/             # 统一返回格式
│   └── exception/            # 异常处理
├── domain/                   # 领域层
│   └── permission/
│       ├── aggregate/        # 聚合根
│       ├── valueobj/         # 值对象
│       ├── service/          # 领域服务
│       └── repository/       # 仓储接口
├── application/              # 应用层
│   └── permission/
│       ├── service/          # 应用服务
│       └── dto/              # 数据传输对象
├── infrastructure/           # 基础设施层
│   └── permission/
│       ├── po/               # 持久化对象
│       ├── mapper/           # MyBatis映射器
│       └── repository/       # 仓储实现
└── interfaces/               # 接口层
    └── permission/           # REST控制器
```

## 核心功能

### 1. 角色管理

#### 默认角色
- **系统管理员**: 拥有所有权限，不可删除
- **平台用户**: 除权限管理外的所有功能
- **访客**: 仅可访问首页

#### 自定义角色
- 支持创建最多1000个自定义角色
- 角色名称长度≤20字符
- 支持菜单权限配置
- 支持启用/禁用状态

#### API接口
```
POST   /api/security-manager/v1/permission/roles              # 创建角色
PUT    /api/security-manager/v1/permission/roles/{roleId}     # 更新角色
DELETE /api/security-manager/v1/permission/roles/{roleId}     # 删除角色
GET    /api/security-manager/v1/permission/roles/{roleId}     # 查询角色详情
GET    /api/security-manager/v1/permission/roles              # 分页查询角色
GET    /api/security-manager/v1/permission/roles/all          # 获取所有角色
```

### 2. 用户管理

#### 核心特性
- 仅允许添加中兴及子公司员工（通过UAC验证）
- 每个用户最多直接分配10个角色
- 支持通过用户组继承角色权限
- 权限计算：直接分配权限 ∪ 用户组继承权限

#### 数据模型
```java
public class User {
    private Long userId;           // 用户ID
    private String username;       // 用户名
    private String userCode;       // 用户工号
    private String organization;   // 组织机构
    private List<Long> roleIds;    // 直接分配的角色
    // ... 其他字段
}
```

### 3. 用户组管理

#### 核心特性
- 支持创建最多1000个用户组
- 每个用户组最多分配10个角色
- 支持用户组成员管理
- 用户组成员自动继承组内所有角色权限

#### 数据模型
```java
public class UserGroup {
    private Long userGroupId;      // 用户组ID
    private String groupName;      // 用户组名称
    private List<Long> roleIds;    // 关联角色
    private List<Long> memberIds;  // 组内成员
    // ... 其他字段
}
```

### 4. 菜单权限

#### 层级编码规则
- 每层使用2位数字表示
- 第1层：01, 02, 03...
- 第2层：0101, 0102, 0201...
- 第3层：010101, 010102...
- 最多支持4层菜单

#### 预置菜单结构
```
01 - 首页 (/workbench)
02 - 权限管理 (/permission)
  ├── 0201 - 角色管理 (/permission/role)
  └── 0202 - 用户（组）管理 (/permission/user)
```

## 数据库设计

### 核心表结构

```sql
-- 角色表
CREATE TABLE t_role (
    role_id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(20) NOT NULL,
    role_description VARCHAR(200),
    role_type VARCHAR(20) DEFAULT 'CUSTOM'
);

-- 用户表
CREATE TABLE t_user (
    user_id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    user_code VARCHAR(50) NOT NULL UNIQUE,
    organization VARCHAR(200)
);

-- 用户组表
CREATE TABLE t_user_group (
    user_group_id BIGSERIAL PRIMARY KEY,
    group_name VARCHAR(50) NOT NULL,
    group_description TEXT
);

-- 菜单表
CREATE TABLE t_menu (
    menu_id VARCHAR(100) PRIMARY KEY,
    menu_name VARCHAR(100) NOT NULL,
    menu_path VARCHAR(200),
    menu_level INTEGER
);

-- 关联表
CREATE TABLE t_role_menu (role_id, menu_id);
CREATE TABLE t_user_role (user_id, role_id);
CREATE TABLE t_user_group_role (user_group_id, role_id);
CREATE TABLE t_user_group_member (user_group_id, user_id);
```

## 统一返回格式

```java
public class ResponseBean<T> {
    private int code;        // 状态码：0-成功，其他-失败
    private String message;  // 返回消息
    private T data;         // 返回数据
    private long timestamp; // 时间戳
}
```

### 使用示例
```java
// 成功返回
return ResponseBeanUtils.success(data);

// 失败返回
return ResponseBeanUtils.error(ResultCode.ROLE_NOT_FOUND);
```

## 业务规则

### 权限控制
1. 默认角色不可删除或重命名
2. 角色被使用时不可删除
3. 自定义角色数量限制：1000个
4. 用户直接分配角色限制：10个
5. 用户组角色分配限制：10个
6. 用户组数量限制：1000个

### 数据验证
1. 角色名称：≤20字符，不可重复
2. 用户工号：必须为中兴员工，不可重复
3. 用户组名称：≤50字符，不可重复
4. 菜单ID：符合层级编码规则

## 部署说明

### 1. 数据库初始化
```bash
# 执行表结构创建脚本
psql -f src/main/resources/dbscript/install/01_permission_tables.sql

# 执行初始化数据脚本
psql -f src/main/resources/dbscript/install/02_permission_init_data.sql
```

### 2. 应用配置
```yaml
# application.yml
spring:
  datasource:
    url: ***********************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
```

### 3. 启动应用
```bash
mvn spring-boot:run
```

## 测试验证

### 单元测试
```bash
mvn test
```

### API测试
使用Swagger UI访问：`http://localhost:8080/swagger-ui.html`

## 扩展功能

### 1. 权限缓存
- 使用Redis缓存用户权限信息
- 权限变更时自动刷新缓存

### 2. 操作日志
- 记录所有权限变更操作
- 支持操作审计和回溯

### 3. 权限验证
- 基于Spring Security的权限拦截
- 支持方法级权限控制

## 注意事项

1. **性能优化**: 避免在循环中执行SQL查询
2. **事务管理**: 关键操作使用@Transactional注解
3. **异常处理**: 统一异常处理和错误码管理
4. **日志记录**: 使用slf4j记录关键操作日志
5. **代码规范**: 方法圈复杂度控制在10以下

## 联系方式

如有问题，请联系开发团队。
